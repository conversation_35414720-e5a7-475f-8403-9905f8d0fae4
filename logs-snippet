Guest dashboard configuration:
guest:1215 - User ID: temp_magic_
guest:1216 - Property ID: 1a344329-2670-4b34-a4f6-e28513a3200c
guest:1217 - WebSocket URL: http://127.0.0.1:8080/
guest:1218 - WebSocket API URL: http://127.0.0.1:8080/
guest:1219 - Guest Name: Eric
guest:1220 - Phone Number: ***-***-9881
guest:1221 - Secure credentials will be loaded via API endpoints
guest:2263 Initialized reservations data: [{…}]
guest:1137 Loading Firebase configuration from secure endpoint...
auth.js:358 Checking authentication state...
auth.js:10 Initializing Firebase securely for auth...
resource-loader.js:89 Firebase already loaded, skipping load from resource-loader
guest_dashboard_main.js:600 === GUEST DASHBOARD INITIALIZATION ===
guest_dashboard_main.js:601 DOM Content Loaded at: 2025-08-02T05:08:22.889Z
guest_dashboard_utils.js:894 Initializing dashboard state from template data
guest_dashboard_utils.js:920 Initialized dashboard state with reservations from template
guest_dashboard_utils.js:921 Dashboard state initialized: {propertyId: '1a344329-2670-4b34-a4f6-e28513a3200c', guestName: 'Guest', phoneNumber: '***-***-9881', reservationsCount: 1}
guest_dashboard_main.js:614 Found reservations in template data, checking for guest info: 1
guest_dashboard_main.js:715 User ID: temp_magic_
guest_dashboard_main.js:716 Guest name: Guest
guest_dashboard_main.js:717 Phone number: Available
guest_dashboard_main.js:736 updateGuestNameDisplay function is properly defined
guest_dashboard_main.js:1866 Updating guest name display...
guest_dashboard_main.js:1882 Using guest name from dashboard state: "Eric" (source: unknown)
guest_dashboard_main.js:1889 Updating dashboardState.guestName from "Guest" to "Eric"
guest_dashboard_main.js:744 Waiting for Firebase to initialize securely...
guest_dashboard_voice_call.js:119 Using Gemini voice: Aoede
guest_dashboard_voice_call.js:120 Using Gemini language: en-US
guest_dashboard_utils.js:837 updateGuestName called with name=Eric, source=magic_link
guest_dashboard_utils.js:864 Updating guest name to "Eric" from source: magic_link
guest_dashboard_utils.js:877 Directly updated DOM element guest-name to "Eric"
guest_dashboard_utils.js:886 Guest name updated successfully to "Eric" from source: magic_link
guest_dashboard_main.js:689 Initialized guest name: "Eric" from source: magic_link
guest_dashboard_main.js:1866 Updating guest name display...
guest_dashboard_main.js:1882 Using guest name from dashboard state: "Eric" (source: magic_link)
guest:1157 Firebase configuration loaded securely
guest:1162 Firebase initialized securely
auth.js:13 Firebase auth initialized securely
guest_dashboard_main.js:746 Firebase initialized successfully
guest_dashboard_main.js:1020 Firebase already initialized, using existing instance
guest_dashboard_text_chat.js:292 initializeSocketIOProcess called
guest_dashboard_text_chat.js:296 Temporary user detected, using temporary ID token
guest_dashboard_text_chat.js:297 Temporary user token available: Yes
guest_dashboard_text_chat.js:298 Temporary user flag: true
guest_dashboard_text_chat.js:304 Stored temporary token in dashboardState and window.storedIdToken
guest_dashboard_text_chat.js:392 checkAndEnableChatButton called
guest_dashboard_text_chat.js:393 - idToken: Present (hidden)
guest_dashboard_text_chat.js:394 - propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:395 - window.storedIdToken: Present (hidden)
guest_dashboard_text_chat.js:396 - window.confirmedPropertyId: Missing
guest_dashboard_text_chat.js:397 - window.isTemporaryUser: true
guest_dashboard_text_chat.js:398 - window.tempIdToken: Present (hidden)
guest_dashboard_text_chat.js:405 Both token and property ID are ready, chat can auto-start when needed
guest_dashboard_text_chat.js:1086 initializeChat called
guest_dashboard_text_chat.js:1096 Chat UI elements found:
guest_dashboard_text_chat.js:1097 - chatInput: Found
guest_dashboard_text_chat.js:1098 - sendMessageButton: Found
guest_dashboard_text_chat.js:1015 Chat connection status: Disconnected
guest_dashboard_text_chat.js:392 checkAndEnableChatButton called
guest_dashboard_text_chat.js:393 - idToken: Present (hidden)
guest_dashboard_text_chat.js:394 - propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:395 - window.storedIdToken: Present (hidden)
guest_dashboard_text_chat.js:396 - window.confirmedPropertyId: Missing
guest_dashboard_text_chat.js:397 - window.isTemporaryUser: true
guest_dashboard_text_chat.js:398 - window.tempIdToken: Present (hidden)
guest_dashboard_text_chat.js:405 Both token and property ID are ready, chat can auto-start when needed
guest_dashboard_text_chat.js:1117 Setting up speech recognition...
guest_dashboard_text_chat.js:210 Web Speech API supported and initialized.
guest_dashboard_text_chat.js:1125 Speech recognition enabled
guest_dashboard_main.js:880 Voice selector not found in the DOM
guest_dashboard_main.js:933 === CHECKING RESERVATION LOADING CONDITION ===
guest_dashboard_main.js:934 reservationsLoaded: undefined
guest_dashboard_main.js:935 isLoadingReservations: undefined
guest_dashboard_main.js:936 CURRENT_USER_ID: temp_magic_
guest_dashboard_main.js:944 First time loading reservations
guest_dashboard_main.js:953 Loading reservations using imported function
guest_dashboard_reservations.js:23 Loading reservations...
guest_dashboard_reservations.js:57 Using user ID for reservation lookup: temp_magic_
guest_dashboard_reservations.js:63 Calling reservations API endpoint: /api/reservations/temp_magic_
guest_dashboard_voice_call.js:291 Initializing voice call system...
guest_dashboard_voice_call.js:311 Using stored language preference: en-US
guest_dashboard_voice_call.js:1065 Voice selector not found in the DOM
guest_dashboard_text_chat.js:392 checkAndEnableChatButton called
guest_dashboard_text_chat.js:393 - idToken: Present (hidden)
guest_dashboard_text_chat.js:394 - propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:395 - window.storedIdToken: Present (hidden)
guest_dashboard_text_chat.js:396 - window.confirmedPropertyId: Missing
guest_dashboard_text_chat.js:397 - window.isTemporaryUser: true
guest_dashboard_text_chat.js:398 - window.tempIdToken: Present (hidden)
guest_dashboard_text_chat.js:405 Both token and property ID are ready, chat can auto-start when needed
guest_dashboard_main.js:1013 Guest dashboard initialization complete
guest_dashboard_voice_call.js:149 Updated language preference from user profile: en-US
guest_dashboard_voice_call.js:2342 === CHECK VOICE CALL READINESS ===
guest_dashboard_voice_call.js:2343 propertyReady: true, propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:2344 userIdReady: true, userId: temp_magic_
guest_dashboard_voice_call.js:2345 currentCallState: idle
guest_dashboard_voice_call.js:2349 Voice call is ready, enabling button
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 12:08 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-02T05:08:24.006Z -> Sat Aug 02 2025
guest:2410 Inserting date separator: Today
guest_dashboard_reservations.js:71 Reservations API response: {reservations: Array(1), success: true}
guest_dashboard_reservations.js:163 Setting current property index to 0
guest_dashboard_reservations.js:180 Keeping existing property ID: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_reservations.js:185 Prefetching details for 1 properties
guest_dashboard_utils.js:209 Fetching property details for 1a344329-2670-4b34-a4f6-e28513a3200c from API
guest_dashboard_utils.js:280 Confirmed property name: Chicago Place
guest_dashboard_utils.js:281 Confirmed property address: 2425 W Lyndale St, Chicago IL 60647
guest_dashboard_reservations.js:799 Property details updated for 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_utils.js:375 Property details updated for 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_reservations.js:324 Rendering reservation cards: [{…}]
guest_dashboard_reservations.js:328 Guest dashboard uses modal-based reservations, skipping main container rendering.
guest_dashboard_reservations.js:639 Updating selected property UI. Current index: 0, Property ID: 1a344329-2670-4b34-a4f6-e28513a3200c
auth.js:377 Auth state changed, user: signed in (jYiuQjA4L9gQIrmH8gty5lGSbA32)
auth.js:392 User is signed in with Firebase. Updating UI.
guest_dashboard_main.js:788 === AUTH STATE CHANGED ===
guest_dashboard_main.js:790 User is signed in: jYiuQjA4L9gQIrmH8gty5lGSbA32
guest_dashboard_main.js:810 Updated phone number from Firebase auth: +17734994970
guest_dashboard_main.js:814 Checking if reservations should be loaded after auth...
guest_dashboard_main.js:822 Reservations already loaded or in progress after auth
guest_dashboard_main.js:827 Running voice call readiness check after auth...
guest_dashboard_voice_call.js:2342 === CHECK VOICE CALL READINESS ===
guest_dashboard_voice_call.js:2343 propertyReady: true, propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:2344 userIdReady: true, userId: jYiuQjA4L9gQIrmH8gty5lGSbA32
guest_dashboard_voice_call.js:2345 currentCallState: idle
guest_dashboard_voice_call.js:2349 Voice call is ready, enabling button
guest_dashboard_main.js:843 Running chat button check after auth...
guest_dashboard_text_chat.js:392 checkAndEnableChatButton called
guest_dashboard_text_chat.js:393 - idToken: Present (hidden)
guest_dashboard_text_chat.js:394 - propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:395 - window.storedIdToken: Present (hidden)
guest_dashboard_text_chat.js:396 - window.confirmedPropertyId: Missing
guest_dashboard_text_chat.js:397 - window.isTemporaryUser: true
guest_dashboard_text_chat.js:398 - window.tempIdToken: Present (hidden)
guest_dashboard_text_chat.js:405 Both token and property ID are ready, chat can auto-start when needed
guest_dashboard_voice_call.js:345 Voice call button clicked, current state: idle
guest_dashboard_voice_call.js:348 === PROPERTY ID DEBUG INFO ===
guest_dashboard_voice_call.js:349 confirmedPropertyId (imported): 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:350 window.PROPERTY_ID: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:351 document.body.dataset.propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:352 window.propertyDetails: Available
guest_dashboard_voice_call.js:365 Requesting ephemeral token for voice call...
guest_dashboard_voice_call.js:1152 Requesting ephemeral token from server...
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 12:08 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-02T05:08:45.242Z -> Sat Aug 02 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-02T05:08:24.006Z -> Sat Aug 02 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:1174 Ephemeral token received successfully
guest_dashboard_voice_call.js:368 Ephemeral token obtained successfully
guest_dashboard_voice_call.js:387 Using authentication token: Present (hidden)
guest_dashboard_voice_call.js:396 Final property ID for voice call: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:401 Microphone access granted.
guest_dashboard_voice_call.js:426 Starting Gemini voice call for property: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:440 Fetching knowledge items for voice call...
guest_dashboard_utils.js:438 Fetching knowledge items for property 1a344329-2670-4b34-a4f6-e28513a3200c from API
guest_dashboard_utils.js:455 Stored raw knowledge items in propertyDetails.knowledgeItems
guest_dashboard_utils.js:468 Retrieved knowledge items from Firestore: 90 items found
guest_dashboard_utils.js:469 Formatted knowledge items stored in window.propertyKnowledgeItems
guest_dashboard_utils.js:473 Sample knowledge items: (3) [{…}, {…}, {…}]
guest_dashboard_voice_call.js:442 Knowledge items fetched successfully for voice call
guest_dashboard_voice_call.js:448 Using property details: Available
guest_dashboard_voice_call.js:449 Property name: Chicago Place
guest_dashboard_voice_call.js:450 Property address: 2425 W Lyndale St, Chicago IL 60647
guest_dashboard_voice_call.js:471 [Deprecation] The ScriptProcessorNode is deprecated. Use AudioWorkletNode instead. (https://bit.ly/audio-worklet)
startGeminiVoiceCall @ guest_dashboard_voice_call.js:471
await in startGeminiVoiceCall
handleVoiceCallClick @ guest_dashboard_voice_call.js:404Understand this warning
guest_dashboard_voice_call.js:501 Ephemeral token received but falling back to API key method for WebSocket connection
guest_dashboard_voice_call.js:1110 Fetching Gemini API key from secure endpoint...
guest_dashboard_voice_call.js:1141 Fetched Gemini API Key successfully from secure endpoint.
guest_dashboard_voice_call.js:506 Attempting WebSocket connection to Gemini Live API with API key: wss://generativelanguage.googleapis.com/ws/google.ai.generativelanguage.v1beta.GenerativeService.BidiGenerateContent?key=REDACTED_KEY&alt=json
guest_dashboard_voice_call.js:512 Using model: gemini-live-2.5-flash-preview
guest_dashboard_voice_call.js:515 Creating WebSocket connection...
guest_dashboard_voice_call.js:826 Voice call successfully started.
guest_dashboard_voice_call.js:527 WebSocket connection established with Gemini voice API
guest_dashboard_voice_call.js:2406 Creating voice conversation session...
guest_dashboard_voice_call.js:2415 Found reservation ID from current reservation: ad157e58-802a-4e26-a3c4-3195274aed81
guest_dashboard_voice_call.js:2432 Phone number for voice conversation: +17734994970
guest_dashboard_voice_call.js:2444 Including reservation ID in voice conversation: ad157e58-802a-4e26-a3c4-3195274aed81
guest_dashboard_voice_call.js:2450 Including phone number in voice conversation: +17734994970
guest_dashboard_voice_call.js:1188 Using shared system prompt function
guest_dashboard_utils.js:571 Creating shared system prompt for property: Chicago Place
guest_dashboard_utils.js:572 Property details available: Yes
guest_dashboard_utils.js:573 Knowledge items available: Yes
guest_dashboard_utils.js:574 createSharedSystemPrompt: Using guest name: Eric
guest_dashboard_utils.js:575 createSharedSystemPrompt: globalDashboardState.guestName: Eric
guest_dashboard_utils.js:576 createSharedSystemPrompt: window.GUEST_NAME: Eric
guest_dashboard_utils.js:580 createSharedSystemPrompt: propertyDetails available: {propertyId: '1a344329-2670-4b34-a4f6-e28513a3200c', name: 'Chicago Place', address: '2425 W Lyndale St, Chicago IL 60647', checkInTime: '15:00', checkOutTime: '11:00', …}
guest_dashboard_utils.js:589 createSharedSystemPrompt: Using knowledge items from globalDashboardState, count: 90
guest_dashboard_utils.js:725 createSharedSystemPrompt: Created comprehensive system prompt with length: 14379
guest_dashboard_voice_call.js:1192 🔧 VOICE SYSTEM PROMPT DEBUG:
guest_dashboard_voice_call.js:1193 📏 Prompt length: 14379
guest_dashboard_voice_call.js:1194 📝 Prompt preview (first 500 chars): You are Staycee, a helpful concierge assistant for Airbnb guests.
Your goal is to provide direct, polite, accurate, and helpful responses to guest inquiries.
IMPORTANT: You are NOT helping a host respond to guests; you ARE the assistant talking directly to guests.
Only provide information that you are confident is correct.
CRITICAL: When a guest asks about WiFi information, ALWAYS provide the complete WiFi network name and password.
IMPORTANT: You MUST share the property address when asked. The 
guest_dashboard_voice_call.js:1195 📝 Prompt preview (last 200 chars): owels, bed linens, soap, and shampoo are provided.

You are assisting the guest with this specific property. The guest is verified and has the right to know all property details including the address.
guest_dashboard_voice_call.js:1203 🔍 System prompt components:
guest_dashboard_voice_call.js:1204   - Property name included: true
guest_dashboard_voice_call.js:1205   - Guest name included: true
guest_dashboard_voice_call.js:1206   - Tools mentioned: true
guest_dashboard_voice_call.js:1207   - RAG context mentioned: true
guest_dashboard_voice_call.js:597 Sending initial configuration to Gemini voice API
guest_dashboard_voice_call.js:598 Using language: en-US for voice call
guest_dashboard_voice_call.js:1835 🔊 Started noise monitoring
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 12:08 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-02T05:08:52.086Z -> Sat Aug 02 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-02T05:08:45.242Z -> Sat Aug 02 2025
guest:2416 No date separator needed for message
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 12:08 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-02T05:08:52.087Z -> Sat Aug 02 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-02T05:08:52.086Z -> Sat Aug 02 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 26)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {setupComplete: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['setupComplete']
guest_dashboard_voice_call.js:2124   - Has serverContent: false
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2130 Gemini setup complete: {}
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 36)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {sessionResumptionUpdate: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['sessionResumptionUpdate']
guest_dashboard_voice_call.js:2124   - Has serverContent: false
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2467 Voice conversation session created: 3a998228-41c1-46c6-a294-36d92edb750f
guest_dashboard_voice_call.js:532 Voice conversation session ready: 3a998228-41c1-46c6-a294-36d92edb750f
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 12:08 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-02T05:08:52.827Z -> Sat Aug 02 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-02T05:08:52.087Z -> Sat Aug 02 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:614 Sending initial greeting message (new session)
guest_dashboard_voice_call.js:615 Greeting message: Hello, my name is Eric. Please greet me by name very briefly!
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1444 Created audio context with sample rate: 24000Hz
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: Hel
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 7888)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 241)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 250)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: lo 
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "lo "
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: Eri
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "Eri"
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1423 Queueing audio for playback (5760 bytes, ~2880 samples, ~0.12s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: c!
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "c!"
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: 

guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0007, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1391 Warning: Converted base64 data contains all zeros in the first 1000 bytes
base64ToArrayBuffer @ guest_dashboard_voice_call.js:1391
processJsonMessageForAudio @ guest_dashboard_voice_call.js:1881
processGeminiJsonMessage @ guest_dashboard_voice_call.js:2278
(anonymous) @ guest_dashboard_voice_call.js:678
Promise.then
geminiWebSocket.onmessage @ guest_dashboard_voice_call.js:665Understand this warning
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: How
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1423 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  ca
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): " ca"
guest_dashboard_voice_call.js:1550 Scheduled audio chunk (0.240s) to play at 1.023, queue length: 0
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: n I
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  he
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: lp 
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 91)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 60)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: you today?
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "you today?"
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 452)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}, usageMetadata: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: (2) ['serverContent', 'usageMetadata']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2153 Model turn complete
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 12:08 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-02T05:08:55.576Z -> Sat Aug 02 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-02T05:08:52.827Z -> Sat Aug 02 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 138)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 36)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {sessionResumptionUpdate: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['sessionResumptionUpdate']
guest_dashboard_voice_call.js:2124   - Has serverContent: false
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2148 Received session resumption handle: CihxZ2t2Zm12YnU2ZzFkcDU2MHZzcm52azNnNHk4dzRubXNpbTY5dm9j
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {sessionResumptionUpdate: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['sessionResumptionUpdate']
guest_dashboard_voice_call.js:2124   - Has serverContent: false
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0008, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received:  Hi
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0553, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 81)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received: ,
guest_dashboard_voice_call.js:2295 📝 Fragment (user): ","
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received:  um
guest_dashboard_voice_call.js:2295 📝 Fragment (user): " um"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 81)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received: ,
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 82)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received:  I
guest_dashboard_voice_call.js:2295 📝 Fragment (user): " I"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received:  was
guest_dashboard_voice_call.js:2295 📝 Fragment (user): " was"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received:  wo
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 87)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received: ndering
guest_dashboard_voice_call.js:2295 📝 Fragment (user): "ndering"
guest_dashboard_voice_call.js:2580 📝 AI Voice Complete: Hello Eric!
How can I help you today?
guest_dashboard_voice_call.js:2507 Storing assistant voice message: Hello Eric!
How can I help you today?
guest_dashboard_voice_call.js:2525 ✅ Successfully stored assistant voice message to DynamoDB
guest_dashboard_voice_call.js:2559 🎯 Displayed assistant voice transcription in chat UI
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 12:08 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-02T05:08:59.581Z -> Sat Aug 02 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-02T05:08:55.576Z -> Sat Aug 02 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received: , uh
guest_dashboard_voice_call.js:2295 📝 Fragment (user): ", uh"
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0492, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 81)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received: ,
guest_dashboard_voice_call.js:2295 📝 Fragment (user): ","
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received:  how
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received:  do
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 82)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received:  I
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 85)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received:  make
guest_dashboard_voice_call.js:2295 📝 Fragment (user): " make"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received:  co
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received: ffee
guest_dashboard_voice_call.js:2295 📝 Fragment (user): "ffee"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received:  for
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received:  my
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received: self
guest_dashboard_voice_call.js:2295 📝 Fragment (user): "self"
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0114, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 81)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received: .
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: I c
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: an 
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: hel
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: p y
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ou 
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "ou "
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: wit
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "wit"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: h t
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: hat
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: . I
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1396 Converted base64 to ArrayBuffer: 11520 bytes
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  wi
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ll 
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "ll "
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1369 Converting base64 to ArrayBuffer (length: 15360)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: che
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ck 
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1423 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: the
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  in
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1369 Converting base64 to ArrayBuffer (length: 15360)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: for
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "for"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 2768)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 82)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 97)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 281)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 368)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: m
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "m"
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ation for you.

guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: I'm
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "I'm"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  so
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: rry
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: , I
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  do
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): " do"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: n't
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "n't"
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  ha
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ve 
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "ve "
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: the
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  in
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): " in"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1423 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: for
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: mat
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "mat"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ion
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  on
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  ho
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 10448)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1423 Queueing audio for playback (7680 bytes, ~3840 samples, ~0.16s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: w 
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0005, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1423 Queueing audio for playback (9600 bytes, ~4800 samples, ~0.20s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: to 
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "to "
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: mak
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "mak"
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: e c
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: off
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1396 Converted base64 to ArrayBuffer: 11520 bytes
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ee.
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "ee."
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  Ho
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: wev
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1423 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: er,
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  I 
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: can
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "can"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1369 Converting base64 to ArrayBuffer (length: 15360)
guest_dashboard_voice_call.js:1423 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  te
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): " te"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1423 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ll 
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1423 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: you
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  th
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): " th"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: at 
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: a G
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: evi
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  4-
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: cup
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1423 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  dr
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ip 
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: cof
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "cof"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: fee
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 10448)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1396 Converted base64 to ArrayBuffer: 7680 bytes
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  m
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1369 Converting base64 to ArrayBuffer (length: 12800)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ake
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "ake"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: r i
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: s a
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "s a"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: vai
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: lab
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: le 
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: for
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  yo
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): " yo"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ur 
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "ur "
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: use
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: . T
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): ". T"
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: o b
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: rew
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "rew"
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  co
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): " co"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ffe
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: e, 
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "e, "
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: mak
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "mak"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: e s
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ure
guest_dashboard_voice_call.js:1571 Audio chunk playback complete
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  th
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): " th"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ere
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  is
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  en
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): " en"
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: oug
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: h w
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "h w"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ate
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: r, 
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: put
guest_dashboard_voice_call.js:1571 Audio chunk playback complete
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 10448)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 83)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1369 Converting base64 to ArrayBuffer (length: 15360)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  co
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ff
guest_dashboard_voice_call.js:1550 Scheduled audio chunk (0.240s) to play at 26.010, queue length: 13
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1396 Converted base64 to ArrayBuffer: 9600 bytes
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ee 
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: in 
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1369 Converting base64 to ArrayBuffer (length: 15360)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: the
guest_dashboard_voice_call.js:1550 Scheduled audio chunk (0.240s) to play at 27.690, queue length: 9
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  fi
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): " fi"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: lte
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: r, 
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "r, "
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: and
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  si
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: mpl
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0005, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: y p
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: res
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1423 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: s t
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "s t"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: he 
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: On/
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "On/"
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: Off
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1396 Converted base64 to ArrayBuffer: 11520 bytes
guest_dashboard_voice_call.js:1423 Queueing audio for playback (11520 bytes, ~5760 samples, ~0.24s duration)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  bu
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): " bu"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: tto
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "tto"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: n. 
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1369 Converting base64 to ArrayBuffer (length: 15360)
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: It 
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "It "
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: use
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1396 Converted base64 to ArrayBuffer: 11520 bytes
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: s c
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: one
guest_dashboard_voice_call.js:1550 Scheduled audio chunk (0.240s) to play at 33.970, queue length: 1
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  fi
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): " fi"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: lte
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1369 Converting base64 to ArrayBuffer (length: 15360)
guest_dashboard_voice_call.js:1396 Converted base64 to ArrayBuffer: 11520 bytes
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: rs.
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "rs."
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received:  Th
guest_dashboard_voice_call.js:1571 Audio chunk playback complete
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: e c
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "e c"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: off
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ee 
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: mak
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: er 
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: aut
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: oma
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "oma"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 13008)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: tic
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: all
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "all"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: y s
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: hut
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: s o
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "s o"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: ff 
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: aft
guest_dashboard_voice_call.js:2580 📝 User Voice Complete: Hi, um, I was wondering, uh, how do I make coffee for myself.
guest_dashboard_voice_call.js:2507 Storing user voice message: Hi, um, I was wondering, uh, how do I make coffee ...
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: er 
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 15568)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 84)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: 2 h
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "2 h"
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 5328)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 82)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: true
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2215 🤖 MODEL TURN DEBUG:
guest_dashboard_voice_call.js:2216   - Has parts: true
guest_dashboard_voice_call.js:2217   - Parts count: 1
guest_dashboard_voice_call.js:2218   - Has candidates: false
guest_dashboard_voice_call.js:2219   - Candidates count: 0
guest_dashboard_voice_call.js:1879 Found inlineData in serverContent.modelTurn.parts
guest_dashboard_voice_call.js:1396 Converted base64 to ArrayBuffer: 3840 bytes
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: o
guest_dashboard_voice_call.js:1550 Scheduled audio chunk (0.240s) to play at 38.690, queue length: 2
guest_dashboard_voice_call.js:2525 ✅ Successfully stored user voice message to DynamoDB
guest_dashboard_text_chat.js:133 displayChatMessage: Using guest name for user message: Eric (window.GUEST_NAME = Eric )
guest_dashboard_voice_call.js:2559 🎯 Displayed user voice transcription in chat UI
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 140)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_text_chat.js:187 Adding date separator for message: user 12:09 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-02T05:09:07.220Z -> Sat Aug 02 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-02T05:08:59.581Z -> Sat Aug 02 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 60)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2161 📝 AI transcription received: urs for boil-dry protection. Also, coffee is in the fridge.
guest_dashboard_voice_call.js:2295 📝 Fragment (ai): "urs for boil-dry protection. Also, coffee is in the fridge."
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:1571 Audio chunk playback complete
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0004, threshold=0.15, noisy=false
2guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:1571 Audio chunk playback complete
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0004, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0005, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:2580 📝 AI Voice Complete: I can help you with that. I will check the information for you.
I'm sorry, I don't have the information on how to make coffee. However, I can tell you that a Gevi 4-cup drip coffee maker is available for your use. To brew coffee, make sure there is enough water, put coffee in the filter, and simply press the On/Off button. It uses cone filters. The coffee maker automatically shuts off after 2 hours for boil-dry protection. Also, coffee is in the fridge.
guest_dashboard_voice_call.js:2507 Storing assistant voice message: I can help you with that. I will check the informa...
guest_dashboard_voice_call.js:2525 ✅ Successfully stored assistant voice message to DynamoDB
guest_dashboard_voice_call.js:2559 🎯 Displayed assistant voice transcription in chat UI
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 12:09 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-02T05:09:12.400Z -> Sat Aug 02 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-02T05:09:07.220Z -> Sat Aug 02 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:1571 Audio chunk playback complete
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0004, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:1571 Audio chunk playback complete
2guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0004, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:1571 Audio chunk playback complete
2guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0005, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:1571 Audio chunk playback complete
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0004, threshold=0.15, noisy=false
2guest_dashboard_voice_call.js:1571 Audio chunk playback complete
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:1571 Audio chunk playback complete
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0006, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:1571 Audio chunk playback complete
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0035, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 53)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2136 User interrupted - clearing audio queue
guest_dashboard_voice_call.js:1808 🚫 Interruption detected (count: 1)
guest_dashboard_voice_call.js:956 🔇 Stopping all audio playback immediately
guest_dashboard_voice_call.js:1010 ✅ All audio playback stopped successfully
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 454)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
3guest_dashboard_voice_call.js:1571 Audio chunk playback complete
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}, usageMetadata: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: (2) ['serverContent', 'usageMetadata']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2153 Model turn complete
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 12:09 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-02T05:09:26.419Z -> Sat Aug 02 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-02T05:09:12.400Z -> Sat Aug 02 2025
guest:2416 No date separator needed for message
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 12:09 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-02T05:09:26.436Z -> Sat Aug 02 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-02T05:09:26.419Z -> Sat Aug 02 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 138)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 36)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {sessionResumptionUpdate: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['sessionResumptionUpdate']
guest_dashboard_voice_call.js:2124   - Has serverContent: false
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2148 Received session resumption handle: CihiYjdydm1lMXBxajF0MW5tdjI5MXMwbDdzZDF1cGcxam83bTc0bTdv
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {sessionResumptionUpdate: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['sessionResumptionUpdate']
guest_dashboard_voice_call.js:2124   - Has serverContent: false
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:661 Received Blob data from Gemini (size: 91)
guest_dashboard_voice_call.js:662 Blob MIME type: unknown
guest_dashboard_voice_call.js:675 Parsed JSON from Blob: {serverContent: {…}}
guest_dashboard_voice_call.js:2121 📨 GEMINI JSON MESSAGE DEBUG:
guest_dashboard_voice_call.js:2122   - Message type: object
guest_dashboard_voice_call.js:2123   - Message keys: ['serverContent']
guest_dashboard_voice_call.js:2124   - Has serverContent: true
guest_dashboard_voice_call.js:2125   - Has modelTurn: false
guest_dashboard_voice_call.js:2126   - Has functionCalls: false
guest_dashboard_voice_call.js:2172 👤 User transcription received:  Thank you.
guest_dashboard_voice_call.js:2295 📝 Fragment (user): " Thank you."
2guest_dashboard_voice_call.js:813 Sent 8192 bytes of audio to Gemini
guest_dashboard_voice_call.js:1688 🔊 Noise analysis: avg=0.0073, threshold=0.15, noisy=false
guest_dashboard_voice_call.js:345 Voice call button clicked, current state: active
guest_dashboard_voice_call.js:348 === PROPERTY ID DEBUG INFO ===
guest_dashboard_voice_call.js:349 confirmedPropertyId (imported): 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:350 window.PROPERTY_ID: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:351 document.body.dataset.propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:352 window.propertyDetails: Available
guest_dashboard_voice_call.js:419 User initiated call end.
guest_dashboard_voice_call.js:836 Stopping voice call. Reason: User ended call
guest_dashboard_voice_call.js:874 Microphone stream stopped.
guest_dashboard_voice_call.js:956 🔇 Stopping all audio playback immediately
guest_dashboard_voice_call.js:1010 ✅ All audio playback stopped successfully
guest_dashboard_voice_call.js:1851 🔇 Stopped noise monitoring and reset state
guest_dashboard_voice_call.js:939 👤 User Complete (final): Thank you.
guest_dashboard_voice_call.js:2580 📝 User Voice Complete: Thank you.
guest_dashboard_voice_call.js:2507 Storing user voice message: Thank you.
guest_dashboard_voice_call.js:2594 Cleaning up voice conversation session: 3a998228-41c1-46c6-a294-36d92edb750f
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 12:09 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-02T05:09:28.358Z -> Sat Aug 02 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-02T05:09:26.436Z -> Sat Aug 02 2025
guest:2416 No date separator needed for message
guest_dashboard_voice_call.js:712 WebSocket closed. Code: 1000, Reason: 
guest_dashboard_voice_call.js:2525 ✅ Successfully stored user voice message to DynamoDB
guest_dashboard_text_chat.js:133 displayChatMessage: Using guest name for user message: Eric (window.GUEST_NAME = Eric )
guest_dashboard_voice_call.js:2559 🎯 Displayed user voice transcription in chat UI
guest_dashboard_text_chat.js:187 Adding date separator for message: user 12:09 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-08-02T05:09:28.601Z -> Sat Aug 02 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-08-02T05:09:28.358Z -> Sat Aug 02 2025
guest:2416 