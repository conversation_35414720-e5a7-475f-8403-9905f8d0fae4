Guest dashboard configuration:
guest:1215 - User ID: temp_magic_
guest:1216 - Property ID: 1a344329-2670-4b34-a4f6-e28513a3200c
guest:1217 - WebSocket URL: https://app.guestrix.ai/
guest:1218 - WebSocket API URL: https://app.guestrix.ai/
guest:1219 - Guest Name: Guest
guest:1220 - Phone Number: ***-***-9881
guest:1221 - Secure credentials will be loaded via API endpoints
guest:2263 Initialized reservations data: Array(1)
guest:1137 Loading Firebase configuration from secure endpoint...
auth.js:358 Checking authentication state...
auth.js:10 Initializing Firebase securely for auth...
resource-loader.js:89 Firebase already loaded, skipping load from resource-loader
guest_dashboard_main.js:600 === GUEST DASHBOARD INITIALIZATION ===
guest_dashboard_main.js:601 DOM Content Loaded at: 2025-07-31T07:26:45.987Z
guest_dashboard_utils.js:782 Initializing dashboard state from template data
guest_dashboard_utils.js:808 Initialized dashboard state with reservations from template
guest_dashboard_utils.js:809 Dashboard state initialized: Object
guest_dashboard_main.js:614 Found reservations in template data, checking for guest info: 1
guest_dashboard_main.js:715 User ID: temp_magic_
guest_dashboard_main.js:716 Guest name: Guest
guest_dashboard_main.js:717 Phone number: Available
guest_dashboard_main.js:736 updateGuestNameDisplay function is properly defined
guest_dashboard_main.js:1866 Updating guest name display...
guest_dashboard_main.js:1882 Using guest name from dashboard state: "Guest" (source: unknown)
guest_dashboard_main.js:744 Waiting for Firebase to initialize securely...
guest_dashboard_voice_call.js:119 Using Gemini voice: Aoede
guest_dashboard_voice_call.js:120 Using Gemini language: en-US
guest_dashboard_utils.js:725 updateGuestName called with name=Guest, source=magic_link
guest_dashboard_utils.js:752 Updating guest name to "Guest" from source: magic_link
guest_dashboard_utils.js:765 Directly updated DOM element guest-name to "Guest"
guest_dashboard_utils.js:774 Guest name updated successfully to "Guest" from source: magic_link
guest_dashboard_main.js:689 Initialized guest name: "Guest" from source: magic_link
guest:1157 Firebase configuration loaded securely
guest:1162 Firebase initialized securely
auth.js:13 Firebase auth initialized securely
guest_dashboard_main.js:746 Firebase initialized successfully
guest_dashboard_main.js:1020 Firebase already initialized, using existing instance
guest_dashboard_text_chat.js:292 initializeSocketIOProcess called
guest_dashboard_text_chat.js:296 Temporary user detected, using temporary ID token
guest_dashboard_text_chat.js:297 Temporary user token available: Yes
guest_dashboard_text_chat.js:298 Temporary user flag: true
guest_dashboard_text_chat.js:304 Stored temporary token in dashboardState and window.storedIdToken
guest_dashboard_text_chat.js:392 checkAndEnableChatButton called
guest_dashboard_text_chat.js:393 - idToken: Present (hidden)
guest_dashboard_text_chat.js:394 - propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:395 - window.storedIdToken: Present (hidden)
guest_dashboard_text_chat.js:396 - window.confirmedPropertyId: Missing
guest_dashboard_text_chat.js:397 - window.isTemporaryUser: true
guest_dashboard_text_chat.js:398 - window.tempIdToken: Present (hidden)
guest_dashboard_text_chat.js:405 Both token and property ID are ready, chat can auto-start when needed
guest_dashboard_text_chat.js:1086 initializeChat called
guest_dashboard_text_chat.js:1096 Chat UI elements found:
guest_dashboard_text_chat.js:1097 - chatInput: Found
guest_dashboard_text_chat.js:1098 - sendMessageButton: Found
guest_dashboard_text_chat.js:1015 Chat connection status: Disconnected
guest_dashboard_text_chat.js:392 checkAndEnableChatButton called
guest_dashboard_text_chat.js:393 - idToken: Present (hidden)
guest_dashboard_text_chat.js:394 - propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:395 - window.storedIdToken: Present (hidden)
guest_dashboard_text_chat.js:396 - window.confirmedPropertyId: Missing
guest_dashboard_text_chat.js:397 - window.isTemporaryUser: true
guest_dashboard_text_chat.js:398 - window.tempIdToken: Present (hidden)
guest_dashboard_text_chat.js:405 Both token and property ID are ready, chat can auto-start when needed
guest_dashboard_text_chat.js:1117 Setting up speech recognition...
guest_dashboard_text_chat.js:210 Web Speech API supported and initialized.
guest_dashboard_text_chat.js:1125 Speech recognition enabled
guest_dashboard_main.js:880 Voice selector not found in the DOM
guest_dashboard_main.js:933 === CHECKING RESERVATION LOADING CONDITION ===
guest_dashboard_main.js:934 reservationsLoaded: undefined
guest_dashboard_main.js:935 isLoadingReservations: undefined
guest_dashboard_main.js:936 CURRENT_USER_ID: temp_magic_
guest_dashboard_main.js:944 First time loading reservations
guest_dashboard_main.js:953 Loading reservations using imported function
guest_dashboard_reservations.js:23 Loading reservations...
guest_dashboard_reservations.js:57 Using user ID for reservation lookup: temp_magic_
guest_dashboard_reservations.js:63 Calling reservations API endpoint: /api/reservations/temp_magic_
guest_dashboard_voice_call.js:291 Initializing voice call system...
guest_dashboard_voice_call.js:311 Using stored language preference: en-US
guest_dashboard_voice_call.js:1065 Voice selector not found in the DOM
guest_dashboard_text_chat.js:392 checkAndEnableChatButton called
guest_dashboard_text_chat.js:393 - idToken: Present (hidden)
guest_dashboard_text_chat.js:394 - propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:395 - window.storedIdToken: Present (hidden)
guest_dashboard_text_chat.js:396 - window.confirmedPropertyId: Missing
guest_dashboard_text_chat.js:397 - window.isTemporaryUser: true
guest_dashboard_text_chat.js:398 - window.tempIdToken: Present (hidden)
guest_dashboard_text_chat.js:405 Both token and property ID are ready, chat can auto-start when needed
guest_dashboard_main.js:1013 Guest dashboard initialization complete
guest_dashboard_voice_call.js:149 Updated language preference from user profile: en-US
guest_dashboard_main.js:1866 Updating guest name display...
guest_dashboard_main.js:1882 Using guest name from dashboard state: "Guest" (source: magic_link)
auth.js:377 Auth state changed, user: signed in (jYiuQjA4L9gQIrmH8gty5lGSbA32)
auth.js:392 User is signed in with Firebase. Updating UI.
guest_dashboard_main.js:788 === AUTH STATE CHANGED ===
guest_dashboard_main.js:790 User is signed in: jYiuQjA4L9gQIrmH8gty5lGSbA32
guest_dashboard_main.js:810 Updated phone number from Firebase auth: +17734994970
guest_dashboard_main.js:814 Checking if reservations should be loaded after auth...
guest_dashboard_main.js:822 Reservations already loaded or in progress after auth
guest_dashboard_main.js:827 Running voice call readiness check after auth...
guest_dashboard_voice_call.js:2229 === CHECK VOICE CALL READINESS ===
guest_dashboard_voice_call.js:2230 propertyReady: true, propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:2231 userIdReady: true, userId: jYiuQjA4L9gQIrmH8gty5lGSbA32
guest_dashboard_voice_call.js:2232 currentCallState: idle
guest_dashboard_voice_call.js:2236 Voice call is ready, enabling button
guest_dashboard_main.js:843 Running chat button check after auth...
guest_dashboard_text_chat.js:392 checkAndEnableChatButton called
guest_dashboard_text_chat.js:393 - idToken: Present (hidden)
guest_dashboard_text_chat.js:394 - propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:395 - window.storedIdToken: Present (hidden)
guest_dashboard_text_chat.js:396 - window.confirmedPropertyId: Missing
guest_dashboard_text_chat.js:397 - window.isTemporaryUser: true
guest_dashboard_text_chat.js:398 - window.tempIdToken: Present (hidden)
guest_dashboard_text_chat.js:405 Both token and property ID are ready, chat can auto-start when needed
guest_dashboard_reservations.js:71 Reservations API response: Object
guest_dashboard_reservations.js:163 Setting current property index to 0
guest_dashboard_reservations.js:180 Keeping existing property ID: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_reservations.js:185 Prefetching details for 1 properties
guest_dashboard_utils.js:209 Fetching property details for 1a344329-2670-4b34-a4f6-e28513a3200c from API
guest_dashboard_utils.js:280 Confirmed property name: Chicago Place
guest_dashboard_utils.js:281 Confirmed property address: 2425 W Lyndale St, Chicago IL 60647
guest_dashboard_reservations.js:799 Property details updated for 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_utils.js:375 Property details updated for 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_reservations.js:324 Rendering reservation cards: Array(1)
guest_dashboard_reservations.js:328 Guest dashboard uses modal-based reservations, skipping main container rendering.
guest_dashboard_reservations.js:639 Updating selected property UI. Current index: 0, Property ID: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:2229 === CHECK VOICE CALL READINESS ===
guest_dashboard_voice_call.js:2230 propertyReady: true, propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_voice_call.js:2231 userIdReady: true, userId: jYiuQjA4L9gQIrmH8gty5lGSbA32
guest_dashboard_voice_call.js:2232 currentCallState: idle
guest_dashboard_voice_call.js:2236 Voice call is ready, enabling button
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 02:26 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-07-31T07:26:46.989Z -> Thu Jul 31 2025
guest:2410 Inserting date separator: Today
guest_dashboard_text_chat.js:914 Chat not connected, starting automatically...
guest_dashboard_text_chat.js:133 displayChatMessage: Using guest name for user message: Guest (window.GUEST_NAME = Guest )
guest_dashboard_text_chat.js:957 Auto-starting chat with URL: https://app.guestrix.ai/
guest_dashboard_text_chat.js:416 initSocket called with: Object
guest_dashboard_utils.js:184 Using cached property details for 1a344329-2670-4b34-a4f6-e28513a3200c from dashboardState.propertyCache
guest_dashboard_reservations.js:799 Property details updated for 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_utils.js:375 Property details updated for 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:443 Verified property details before socket connection: Object
guest_dashboard_text_chat.js:447 Fetching knowledge items for text chat...
guest_dashboard_utils.js:438 Fetching knowledge items for property 1a344329-2670-4b34-a4f6-e28513a3200c from API
guest_dashboard_text_chat.js:187 Adding date separator for message: user 02:26 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-07-31T07:26:54.039Z -> Thu Jul 31 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-07-31T07:26:46.989Z -> Thu Jul 31 2025
guest:2416 No date separator needed for message
guest_dashboard_utils.js:455 Stored raw knowledge items in propertyDetails.knowledgeItems
guest_dashboard_utils.js:468 Retrieved knowledge items from Firestore: 90 items found
guest_dashboard_utils.js:469 Formatted knowledge items stored in window.propertyKnowledgeItems
guest_dashboard_utils.js:473 Sample knowledge items: Array(3)
guest_dashboard_text_chat.js:454 Knowledge items fetched successfully for text chat
guest_dashboard_text_chat.js:468 Socket.IO connection strategy: Using URL https://app.guestrix.ai/
guest_dashboard_text_chat.js:469 Using auth token: eyJhbGciOi...
guest_dashboard_text_chat.js:470 Using property ID: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:483 Guest name for Socket.IO auth: Guest
guest_dashboard_text_chat.js:493 Extracted user ID from token: temp_magic_
guest_dashboard_text_chat.js:514 Converted WebSocket URL to HTTP/HTTPS for Socket.IO: https://app.guestrix.ai/
guest_dashboard_text_chat.js:631 Socket.IO connection successful: Object
guest_dashboard_text_chat.js:547 Socket.IO connection OPENED
guest_dashboard_text_chat.js:1015 Chat connection status: Connected
guest_dashboard_utils.js:571 Creating shared system prompt for property: Chicago Place
guest_dashboard_utils.js:572 Property details available: Yes
guest_dashboard_utils.js:573 Knowledge items available: Yes
guest_dashboard_utils.js:574 createSharedSystemPrompt: Using guest name: Guest
guest_dashboard_utils.js:575 createSharedSystemPrompt: globalDashboardState.guestName: Guest
guest_dashboard_utils.js:576 createSharedSystemPrompt: window.GUEST_NAME: Guest
guest_dashboard_utils.js:580 createSharedSystemPrompt: propertyDetails available: Object
guest_dashboard_utils.js:586 createSharedSystemPrompt: Using knowledge items from globalDashboardState, count: 90
guest_dashboard_utils.js:613 createSharedSystemPrompt: Created system prompt with length: 1714
guest_dashboard_text_chat.js:565 Found reservation ID from current reservation: ad157e58-802a-4e26-a3c4-3195274aed81
guest_dashboard_text_chat.js:582 Phone number for auth: +17734994970
guest_dashboard_text_chat.js:613 Sent auth message with user_id, property_id, and reservation_id: ad157e58-802a-4e26-a3c4-3195274aed81
guest_dashboard_text_chat.js:622 Sent configure_tools message
guest_dashboard_text_chat.js:392 checkAndEnableChatButton called
guest_dashboard_text_chat.js:393 - idToken: Present (hidden)
guest_dashboard_text_chat.js:394 - propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:395 - window.storedIdToken: Present (hidden)
guest_dashboard_text_chat.js:396 - window.confirmedPropertyId: Missing
guest_dashboard_text_chat.js:397 - window.isTemporaryUser: true
guest_dashboard_text_chat.js:398 - window.tempIdToken: Present (hidden)
guest_dashboard_text_chat.js:405 Both token and property ID are ready, chat can auto-start when needed
guest_dashboard_text_chat.js:1015 Chat connection status: Connected
guest_dashboard_text_chat.js:645 Tools configuration response: Object
guest_dashboard_text_chat.js:37 Retrieved conversation history: Object
guest_dashboard_text_chat.js:598 Sent auth message with conversation history: 18 previous conversations and 1 current messages
guest_dashboard_text_chat.js:636 Authentication successful: Object
guest_dashboard_text_chat.js:962 Chat auto-started successfully, sending pending message
guest_dashboard_text_chat.js:970 Sent pending message via Socket.IO: what is this place called? for property: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:789 Updated last activity timestamp
guest_dashboard_text_chat.js:711 Text message from AI via Socket.IO: Object
guest_dashboard_text_chat.js:789 Updated last activity timestamp
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 02:27 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-07-31T07:27:04.966Z -> Thu Jul 31 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-07-31T07:26:54.039Z -> Thu Jul 31 2025
guest:2416 No date separator needed for message
guest_dashboard_text_chat.js:133 displayChatMessage: Using guest name for user message: Guest (window.GUEST_NAME = Guest )
guest_dashboard_text_chat.js:1005 Sent text message via Socket.IO: who is the host? what are wifi credentials? for property: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:789 Updated last activity timestamp
guest_dashboard_text_chat.js:187 Adding date separator for message: user 02:27 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-07-31T07:27:14.927Z -> Thu Jul 31 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-07-31T07:27:04.966Z -> Thu Jul 31 2025
guest:2416 No date separator needed for message
guest_dashboard_text_chat.js:711 Text message from AI via Socket.IO: Object
guest_dashboard_text_chat.js:789 Updated last activity timestamp
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 02:27 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-07-31T07:27:25.320Z -> Thu Jul 31 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-07-31T07:27:14.927Z -> Thu Jul 31 2025
guest:2416 No date separator needed for message
guest_dashboard_text_chat.js:133 displayChatMessage: Using guest name for user message: Guest (window.GUEST_NAME = Guest )
guest_dashboard_text_chat.js:1005 Sent text message via Socket.IO: what is the property name? for property: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:789 Updated last activity timestamp
guest_dashboard_text_chat.js:187 Adding date separator for message: user 02:27 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-07-31T07:27:36.408Z -> Thu Jul 31 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-07-31T07:27:25.320Z -> Thu Jul 31 2025
guest:2416 No date separator needed for message
guest_dashboard_text_chat.js:711 Text message from AI via Socket.IO: Object
guest_dashboard_text_chat.js:789 Updated last activity timestamp
guest_dashboard_text_chat.js:187 Adding date separator for message: assistant 02:27 AM
guest:2368 addDateSeparatorIfNeeded: Using stored timestamp 2025-07-31T07:27:46.746Z -> Thu Jul 31 2025
guest:2389 addDateSeparatorIfNeeded: Previous message timestamp 2025-07-31T07:27:36.408Z -> Thu Jul 31 2025
guest:2416 No date separator needed for message
guest_dashboard_text_chat.js:803 Inactive for 2 minutes, closing WebSocket connection silently.
guest_dashboard_text_chat.js:1015 Chat connection status: Disconnected
guest_dashboard_text_chat.js:822 Sent inactivity disconnect notification to server
guest_dashboard_text_chat.js:667 Socket.IO connection CLOSED. Reason: io client disconnect
guest_dashboard_text_chat.js:1015 Chat connection status: Disconnected
guest_dashboard_text_chat.js:392 checkAndEnableChatButton called
guest_dashboard_text_chat.js:393 - idToken: Present (hidden)
guest_dashboard_text_chat.js:394 - propertyId: 1a344329-2670-4b34-a4f6-e28513a3200c
guest_dashboard_text_chat.js:395 - window.storedIdToken: Present (hidden)
guest_dashboard_text_chat.js:396 - window.confirmedPropertyId: Missing
guest_dashboard_text_chat.js:397 - window.isTemporaryUser: true
guest_dashboard_text_chat.js:398 - window.tempIdToken: Present (hidden)
guest_dashboard_text_chat.js:405 Both token and property ID are ready, chat can auto-start when needed
guest_dashboard_text_chat.js:1015 Chat connection status: Disconnected
guest_dashboard_text_chat.js:679 This was an intentional disconnect, not showing reconnection message
guest_dashboard_text_chat.js:831 Socket.IO connection disconnected due to inactivity