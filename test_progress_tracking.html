<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Progress Tracking Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .persian-green { background-color: #00a693; }
        .dark-purple { color: #2d1b69; }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-2xl font-bold text-center mb-8">Enhanced Progress Tracking Test</h1>
        
        <div class="bg-white rounded-lg shadow-lg p-6">
            <button onclick="testProgressTracking()" class="w-full bg-persian-green text-white py-3 px-6 rounded-lg font-medium hover:bg-green-600 transition-colors">
                Test Enhanced Progress Tracking
            </button>
            
            <div id="progress-container" class="hidden mt-6">
                <!-- Enhanced warning message -->
                <div class="bg-red-50 border-2 border-red-200 rounded-xl p-6 max-w-lg mx-auto mb-6">
                    <div class="flex items-center justify-center mb-3">
                        <i class="fas fa-exclamation-triangle text-red-500 text-2xl mr-3"></i>
                        <h3 class="text-lg font-bold text-red-800">CAUTION! Don't Close This Window</h3>
                    </div>
                    <p class="text-sm text-red-700 mb-3">
                        We're extracting detailed information from your properties. This process takes time because we're gathering amenities, house rules, descriptions, and more.
                    </p>
                    <div class="flex items-center justify-center space-x-4 text-xs text-red-600">
                        <div class="flex items-center space-x-1">
                            <i id="activity-icon-1" class="fas fa-coffee text-amber-600"></i>
                            <span>Grab some coffee</span>
                        </div>
                        <div class="flex items-center space-x-1">
                            <i id="activity-icon-2" class="fas fa-dumbbell text-blue-600"></i>
                            <span>Do a quick stretch</span>
                        </div>
                        <div class="flex items-center space-x-1">
                            <i id="activity-icon-3" class="fas fa-heart text-pink-600"></i>
                            <span>Pet your furry friend</span>
                        </div>
                    </div>
                </div>

                <!-- Dynamic progress status -->
                <div class="mb-4 text-center">
                    <p id="progress-status" class="text-gray-600 font-medium">Preparing to import 3 properties...</p>
                    <p id="progress-detail" class="text-sm text-gray-500 mt-1">Initializing extraction process...</p>
                </div>

                <!-- Enhanced progress indicator -->
                <div class="mt-6 max-w-md mx-auto">
                    <div class="flex justify-between text-xs text-gray-500 mb-2">
                        <span id="progress-current">0</span>
                        <span id="progress-total">3 properties</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3 shadow-inner">
                        <div id="progress-bar" class="h-3 rounded-full transition-all duration-500 ease-out" style="width: 0%; background: linear-gradient(to right, #00a693, #10b981);"></div>
                    </div>
                    <div class="text-xs text-gray-400 mt-1 text-center">
                        <span id="progress-percentage">0%</span> complete
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Progress state
        let progressState = {
            currentProperty: 0,
            totalProperties: 0,
            currentStep: '',
            activityIconIndex: 0,
            activityIconInterval: null
        };

        function startActivityIconRotation() {
            const icons = [
                { id: 'activity-icon-1', icon: 'fas fa-coffee', color: 'text-amber-600' },
                { id: 'activity-icon-2', icon: 'fas fa-dumbbell', color: 'text-blue-600' },
                { id: 'activity-icon-3', icon: 'fas fa-heart', color: 'text-pink-600' }
            ];
            
            progressState.activityIconInterval = setInterval(() => {
                // Reset all icons to default state
                icons.forEach(iconData => {
                    const element = document.getElementById(iconData.id);
                    if (element) {
                        element.className = `${iconData.icon} ${iconData.color}`;
                    }
                });
                
                // Highlight current icon
                const currentIcon = icons[progressState.activityIconIndex];
                const element = document.getElementById(currentIcon.id);
                if (element) {
                    element.className = `${currentIcon.icon} ${currentIcon.color} animate-bounce text-lg`;
                }
                
                progressState.activityIconIndex = (progressState.activityIconIndex + 1) % icons.length;
            }, 2000);
        }

        function updateProgressDisplay() {
            const progressBar = document.getElementById('progress-bar');
            const progressPercentage = document.getElementById('progress-percentage');
            const progressCurrent = document.getElementById('progress-current');
            const progressStatus = document.getElementById('progress-status');
            const progressDetail = document.getElementById('progress-detail');
            
            if (!progressBar) return;
            
            const percentage = progressState.totalProperties > 0 
                ? Math.round((progressState.currentProperty / progressState.totalProperties) * 100)
                : 0;
            
            if (progressCurrent) {
                progressCurrent.textContent = `${progressState.currentProperty}`;
            }
            
            if (progressStatus) {
                if (progressState.currentProperty === 0) {
                    progressStatus.textContent = `Preparing to import ${progressState.totalProperties} properties...`;
                } else if (progressState.currentProperty < progressState.totalProperties) {
                    progressStatus.textContent = `Processing property ${progressState.currentProperty} of ${progressState.totalProperties}`;
                } else {
                    progressStatus.textContent = `Finalizing import of ${progressState.totalProperties} properties...`;
                }
            }
            
            if (progressDetail) {
                progressDetail.textContent = progressState.currentStep || 'Initializing extraction process...';
            }
        }

        function startProgressSimulation(totalProperties) {
            progressState.totalProperties = totalProperties;
            progressState.currentProperty = 0;
            
            const steps = [
                { delay: 1000, step: 'Connecting to Airbnb...', progress: 0.05 },
                { delay: 2000, step: 'Loading property pages...', progress: 0.15 },
                { delay: 4000, step: 'Extracting amenities and features...', progress: 0.35 },
                { delay: 5000, step: 'Processing house rules and modals...', progress: 0.55 },
                { delay: 4000, step: 'Gathering safety information...', progress: 0.70 },
                { delay: 3000, step: 'Cleaning and organizing descriptions...', progress: 0.85 },
                { delay: 2000, step: 'Finalizing property data...', progress: 0.95 }
            ];
            
            let currentStepIndex = 0;
            let currentPropertyIndex = 1;
            
            function simulateNextStep() {
                if (currentStepIndex < steps.length) {
                    const step = steps[currentStepIndex];
                    const propertyProgress = (currentPropertyIndex - 1) / totalProperties;
                    const stepProgress = step.progress / totalProperties;
                    const totalProgress = propertyProgress + stepProgress;
                    
                    progressState.currentProperty = Math.min(currentPropertyIndex, totalProperties);
                    progressState.currentStep = totalProperties > 1 
                        ? `Property ${currentPropertyIndex}: ${step.step}`
                        : step.step;
                    
                    updateProgressDisplay();
                    
                    const progressBar = document.getElementById('progress-bar');
                    if (progressBar) {
                        const percentage = Math.min(Math.round(totalProgress * 100), 95);
                        progressBar.style.width = `${percentage}%`;
                        
                        const progressPercentage = document.getElementById('progress-percentage');
                        if (progressPercentage) {
                            progressPercentage.textContent = `${percentage}%`;
                        }
                    }
                    
                    currentStepIndex++;
                    
                    if (currentStepIndex >= steps.length && currentPropertyIndex < totalProperties) {
                        currentPropertyIndex++;
                        currentStepIndex = 0;
                    }
                    
                    if (currentPropertyIndex <= totalProperties) {
                        setTimeout(simulateNextStep, step.delay);
                    } else {
                        // Complete
                        setTimeout(() => {
                            progressState.currentStep = 'Import completed successfully!';
                            updateProgressDisplay();
                            const progressBar = document.getElementById('progress-bar');
                            if (progressBar) {
                                progressBar.style.width = '100%';
                                const progressPercentage = document.getElementById('progress-percentage');
                                if (progressPercentage) {
                                    progressPercentage.textContent = '100%';
                                }
                            }
                            clearInterval(progressState.activityIconInterval);
                        }, 1000);
                    }
                }
            }
            
            setTimeout(simulateNextStep, 500);
        }

        function testProgressTracking() {
            const container = document.getElementById('progress-container');
            container.classList.remove('hidden');
            
            startActivityIconRotation();
            startProgressSimulation(3);
        }
    </script>
</body>
</html>
