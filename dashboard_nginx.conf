server {
    server_name app.guestrix.ai;

    location / {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 900s;
        proxy_send_timeout 900s;
        proxy_connect_timeout 60s;
    }

    listen 443 ssl;
    ssl_certificate /etc/letsencrypt/live/app.guestrix.ai/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/app.guestrix.ai/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
}

server {
    if ($host = app.guestrix.ai) {
        return 301 https://$host$request_uri;
    }

    server_name app.guestrix.ai;
    listen 80;
    return 404;
}
