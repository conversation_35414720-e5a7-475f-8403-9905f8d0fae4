# Voice Call Implementation Branch Comparison

## Overview
This document compares the `guest_dashboard_voice_call.js` file across four branches:
- `main`
- `development` 
- `SCRUM-29-fix-background-noise` (current)
- `SCRUM-33-beta-trials`

## Key Differences Summary

### 1. Gemini Live Model

| Branch | Model | Description |
|--------|-------|-------------|
| `main` | `gemini-2.0-flash-live-001` | Original live voice model |
| `development` | `gemini-2.0-flash-live-001` | Same as main |
| `SCRUM-29-fix-background-noise` | `gemini-2.5-flash-preview-native-audio-dialog` | **NEW**: Native audio dialog model with better noise handling |
| `SCRUM-33-beta-trials` | `gemini-2.5-flash-preview-native-audio-dialog` | Same as SCRUM-29 |

**Impact**: The newer model in SCRUM-29 and SCRUM-33 provides better noise handling and audio quality.

### 2. Noise Management System

| Branch | Noise Management | Status |
|--------|------------------|--------|
| `main` | ❌ None | No noise detection or management |
| `development` | ❌ None | No noise detection or management |
| `SCRUM-29-fix-background-noise` | ✅ **FULL IMPLEMENTATION** | Complete noise management system |
| `SCRUM-33-beta-trials` | ✅ **FULL IMPLEMENTATION** | Complete noise management system |

#### Noise Management Features (SCRUM-29 & SCRUM-33):

**Constants Added:**
```javascript
const NOISE_LEVEL_THRESHOLD = 0.15;  // Threshold for detecting noisy environment
const NOISE_SAMPLES_COUNT = 10;  // Number of samples to analyze for noise level
const INTERRUPTION_THRESHOLD = 3;  // Number of interruptions before suggesting text chat
const NOISE_ANALYSIS_INTERVAL = 2000;  // Analyze noise every 2 seconds
```

**Variables Added:**
```javascript
let noiseLevels = [];
let currentNoiseLevel = 0;
let interruptionCount = 0;
let isNoisyEnvironment = false;
let noiseAnalysisTimer = null;
let hasOfferedTextChat = false;
let lastNoiseWarning = 0;
```

**New Functions:**
- `detectNoiseLevel(audioBuffer)` - Calculates RMS noise level from audio data
- `analyzeEnvironmentNoise()` - Analyzes noise patterns and adjusts VAD settings
- `adjustVADForNoisy()` - Adjusts VAD settings for noisy environments
- `adjustVADForQuiet()` - Resets VAD settings for quiet environments
- `showNoiseWarning()` - Shows noise warning to user
- `hideNoiseWarning()` - Hides noise warning
- `handleInterruption()` - Handles interruption events for noise management
- `startNoiseMonitoring()` - Starts periodic noise analysis
- `stopNoiseMonitoring()` - Stops noise monitoring

### 3. Interruption Handling

| Branch | Interruption Handling | Status |
|--------|---------------------|--------|
| `main` | ❌ Basic | Only stops audio playback |
| `development` | ❌ Basic | Only stops audio playback |
| `SCRUM-29-fix-background-noise` | ✅ **ENHANCED** | Full interruption management with noise context |
| `SCRUM-33-beta-trials` | ✅ **ENHANCED** | Full interruption management with noise context |

**Enhanced Interruption Handling (SCRUM-29 & SCRUM-33):**
```javascript
// In WebSocket message handler
if (jsonMessage.serverContent && jsonMessage.serverContent.interrupted === true) {
    console.log("User interrupted - clearing audio queue");
    // Handle interruption for noise management
    handleInterruption();
    // Stop active playback immediately and aggressively
    stopAllAudioPlayback();
    addMessageToChat("Listening...", "ai");
    return;
}
```

### 4. VAD (Voice Activity Detection) Configuration

| Branch | VAD Configuration | Status |
|--------|------------------|--------|
| `main` | ❌ Static | Fixed VAD settings |
| `development` | ❌ Static | Fixed VAD settings |
| `SCRUM-29-fix-background-noise` | ✅ **DYNAMIC** | Adaptive VAD based on noise levels |
| `SCRUM-33-beta-trials` | ✅ **DYNAMIC** | Adaptive VAD based on noise levels |

**Dynamic VAD Settings:**

**For Noisy Environments:**
```javascript
startOfSpeechSensitivity: "START_SENSITIVITY_MEDIUM", // Reduced from HIGH
endOfSpeechSensitivity: "END_SENSITIVITY_MEDIUM",     // Increased from LOW
prefixPaddingMs: 200,  // Increased from 50
silenceDurationMs: 1000 // Increased from 500
```

**For Quiet Environments:**
```javascript
startOfSpeechSensitivity: "START_SENSITIVITY_HIGH", // Back to HIGH
endOfSpeechSensitivity: "END_SENSITIVITY_LOW",      // Back to LOW
prefixPaddingMs: 50,   // Back to 50
silenceDurationMs: 500  // Back to 500
```

### 5. Authentication Method

| Branch | Authentication | Method |
|--------|----------------|--------|
| `main` | Direct API Key | Uses `window.GEMINI_API_KEY` directly |
| `development` | Direct API Key | Uses `window.GEMINI_API_KEY` directly |
| `SCRUM-29-fix-background-noise` | Direct API Key | Uses `window.GEMINI_API_KEY` directly |
| `SCRUM-33-beta-trials` | **Ephemeral Token** | Uses secure ephemeral token system |

**SCRUM-33-beta-trials Authentication:**
```javascript
// Uses ephemeral token system
const tokenResponse = await fetchEphemeralToken();
authToken = tokenResponse.token;

// Has fallback to direct API key
async function fetchEphemeralToken() {
    const response = await fetch('/api/ephemeral-token', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        credentials: 'same-origin'
    });
    // ... token processing
}
```

### 6. System Prompt Enhancements

| Branch | System Prompt | Status |
|--------|---------------|--------|
| `main` | ❌ Basic | Standard system prompt |
| `development` | ❌ Basic | Standard system prompt |
| `SCRUM-29-fix-background-noise` | ✅ **ENHANCED** | Added voice call guidelines |
| `SCRUM-33-beta-trials` | ✅ **ENHANCED** | Added voice call guidelines |

**Enhanced System Prompt (SCRUM-29 & SCRUM-33):**
```javascript
IMPORTANT VOICE CALL GUIDELINES:
- If you detect background noise or environmental sounds that may interfere with our conversation, politely acknowledge it
- If there are repeated interruptions due to background noise, suggest the guest might prefer text chat
- Be patient with audio quality issues and don't interrupt the guest unnecessarily
- If the guest seems to be in a noisy environment (traffic, crowds, etc.), offer to help via text chat instead
```

## Summary of Branch Evolution

### Main Branch
- **Purpose**: Production baseline
- **Features**: Basic voice call functionality with older Gemini model
- **Limitations**: No noise management, static VAD settings

### Development Branch  
- **Purpose**: Development baseline
- **Features**: Same as main branch
- **Status**: Appears to be identical to main

### SCRUM-29-fix-background-noise Branch
- **Purpose**: **Background noise mitigation and interruption handling**
- **Key Improvements**:
  - Upgraded to `gemini-2.5-flash-preview-native-audio-dialog` model
  - Complete noise management system with real-time analysis
  - Dynamic VAD configuration based on environment
  - Enhanced interruption handling with noise context
  - Improved system prompt with noise-aware guidelines
- **Status**: **Most advanced noise management implementation**

### SCRUM-33-beta-trials Branch
- **Purpose**: **Beta testing with enhanced security**
- **Key Improvements**:
  - All features from SCRUM-29
  - **Plus**: Secure ephemeral token authentication system
  - **Plus**: Fallback authentication for compatibility
- **Status**: **Most secure implementation with full noise management**

## Recommendations

1. **For Production**: Consider merging SCRUM-33-beta-trials as it has both noise management and enhanced security
2. **For Testing**: Use SCRUM-29-fix-background-noise for noise management testing without security changes
3. **For Development**: Update main/development branches to include noise management features from SCRUM-29

## Technical Debt

- Main and development branches are significantly behind in noise management capabilities
- Consider consolidating the noise management improvements across all branches
- The ephemeral token system in SCRUM-33 should be evaluated for production readiness 