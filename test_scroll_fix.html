<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scroll-to-Top Fix Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .persian-green { color: #00a693; }
        .focus\:ring-persian-green:focus { --tw-ring-color: #00a693; }
        .focus\:border-persian-green:focus { --tw-border-opacity: 1; border-color: #00a693; }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold text-center mb-8">Property Setup Modal Scroll Fix Test</h1>
        
        <button onclick="openTestModal()" 
                class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-6 rounded-lg font-medium mb-4">
            Open Test Modal
        </button>

        <!-- Test Modal (mimics Property Setup Modal structure) -->
        <div id="property-setup-modal" class="fixed inset-0 bg-black bg-opacity-75 z-[90] flex items-center justify-center p-4" style="display: none;">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                <!-- Header -->
                <div class="p-6 text-white" style="background: linear-gradient(to right, #2a9d8f, #e9c46a);">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-xl font-semibold text-white">
                                <i class="fas fa-cog mr-2"></i>Property Setup
                            </h3>
                            <p class="text-white opacity-90 text-sm mt-1">Test Property</p>
                        </div>
                        <button onclick="closeTestModal()" class="text-white hover:text-gray-200">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>

                <!-- Content Area (this is the scrollable part) -->
                <div id="setup-step-content" class="p-6 overflow-y-auto" style="max-height: 60vh;">
                    <!-- Error container (will be populated by validation) -->
                    <div id="error-container"></div>
                    
                    <!-- Form content -->
                    <div class="space-y-6">
                        <h2 class="text-xl font-semibold text-gray-900 mb-4">Basic Information</h2>
                        
                        <!-- Property Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Property Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="property-name" value="Test Property"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green">
                        </div>

                        <!-- iCal URL -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                iCal URL (Reservation Sync) <span class="text-red-500">*</span>
                            </label>
                            <input type="url" id="ical-url" value=""
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-persian-green focus:border-persian-green"
                                   placeholder="https://www.airbnb.com/calendar/ical/..." required>
                        </div>

                        <!-- Lots of filler content to make scrolling necessary -->
                        <div class="space-y-4">
                            <h3 class="text-lg font-medium text-gray-900">Filler Content</h3>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Field 1</label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Field 2</label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Large Text Area</label>
                                <textarea rows="6" class="w-full px-3 py-2 border border-gray-300 rounded-lg"
                                          placeholder="This is filler content to make the modal scrollable..."></textarea>
                            </div>
                            <div class="grid grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Field 3</label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Field 4</label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Field 5</label>
                                    <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-lg">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Another Large Text Area</label>
                                <textarea rows="8" class="w-full px-3 py-2 border border-gray-300 rounded-lg"
                                          placeholder="More filler content to ensure scrolling is needed..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="bg-gray-50 px-6 py-4 flex justify-between items-center border-t">
                    <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        <i class="fas fa-arrow-left mr-2"></i>Previous
                    </button>
                    
                    <div class="text-sm text-gray-600">
                        Step 1 of 5: Basic Information
                    </div>
                    
                    <button onclick="testValidation()" 
                            class="px-4 py-2 bg-persian-green text-white rounded-lg hover:bg-persian-green/90 transition-colors">
                        Next <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 class="font-semibold text-blue-800 mb-2">Test Instructions:</h3>
            <ol class="text-sm text-blue-700 list-decimal list-inside space-y-1">
                <li>Click "Open Test Modal"</li>
                <li>Scroll down to the bottom of the modal content</li>
                <li>Click "Next" button (validation will fail due to empty iCal)</li>
                <li>Check if the modal scrolls to the top to show the error message</li>
                <li>Check browser console for scroll debug messages</li>
            </ol>
            <div id="test-results" class="mt-4 p-3 bg-white rounded border">
                Test results will appear here...
            </div>
        </div>
    </div>

    <script>
        function openTestModal() {
            document.getElementById('property-setup-modal').style.display = 'flex';
            document.getElementById('test-results').innerHTML = '✅ Modal opened. Scroll down and test validation.';
        }

        function closeTestModal() {
            document.getElementById('property-setup-modal').style.display = 'none';
            document.getElementById('test-results').innerHTML = '❌ Modal closed.';
        }

        function testValidation() {
            const icalUrl = document.getElementById('ical-url').value.trim();
            const propertyName = document.getElementById('property-name').value.trim();

            console.log('Testing validation...');
            console.log('iCal URL:', icalUrl);
            console.log('Property Name:', propertyName);

            const validationErrors = [];

            if (!propertyName) {
                validationErrors.push('Property Name is required');
                highlightField('property-name');
            } else {
                clearHighlight('property-name');
            }

            if (!icalUrl) {
                validationErrors.push('iCal URL is required for reservation management');
                highlightField('ical-url');
            } else {
                clearHighlight('ical-url');
            }

            if (validationErrors.length > 0) {
                console.log('Validation errors found:', validationErrors);
                showValidationErrors(validationErrors);
                scrollToTop();
                document.getElementById('test-results').innerHTML = '🔍 Validation failed. Check if modal scrolled to top to show errors.';
            } else {
                clearErrors();
                document.getElementById('test-results').innerHTML = '✅ All validation passed!';
            }
        }

        function showValidationErrors(errors) {
            clearErrors();

            const errorDiv = document.createElement('div');
            errorDiv.id = 'validation-errors';
            errorDiv.className = 'bg-red-50 border border-red-200 rounded-lg p-4 mb-4';
            errorDiv.innerHTML = `
                <div class="flex items-start space-x-3">
                    <i class="fas fa-exclamation-triangle text-red-600 mt-1"></i>
                    <div>
                        <h4 class="text-sm font-medium text-red-800 mb-1">Please fix the following errors:</h4>
                        <ul class="text-sm text-red-700 list-disc list-inside">
                            ${errors.map(error => `<li>${error}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;

            const stepContent = document.getElementById('setup-step-content');
            if (stepContent) {
                stepContent.insertBefore(errorDiv, stepContent.firstChild);
            }
        }

        function clearErrors() {
            const errorDiv = document.getElementById('validation-errors');
            if (errorDiv) {
                errorDiv.remove();
            }
            
            ['property-name', 'ical-url'].forEach(clearHighlight);
        }

        function highlightField(fieldId) {
            const field = document.getElementById(fieldId);
            if (field) {
                field.classList.remove('border-gray-300');
                field.classList.add('border-red-500', 'border-2');
            }
        }

        function clearHighlight(fieldId) {
            const field = document.getElementById(fieldId);
            if (field) {
                field.classList.remove('border-red-500', 'border-2');
                field.classList.add('border-gray-300');
            }
        }

        function scrollToTop() {
            console.log('scrollToTop() called');
            
            // Primary target: the scrollable step content container
            const stepContent = document.getElementById('setup-step-content');
            if (stepContent) {
                stepContent.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
                console.log('✅ Scrolled setup-step-content to top');
                return;
            }

            // Fallback 1: try the modal's inner content div
            const modalInner = document.querySelector('#property-setup-modal .bg-white.rounded-lg');
            if (modalInner) {
                modalInner.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
                console.log('✅ Scrolled modal inner content to top');
                return;
            }

            // Fallback 2: scroll the entire modal
            const modal = document.getElementById('property-setup-modal');
            if (modal) {
                modal.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
                console.log('✅ Scrolled entire modal to top');
            } else {
                console.warn('❌ Could not find any scroll target for validation errors');
            }
        }
    </script>
</body>
</html>
