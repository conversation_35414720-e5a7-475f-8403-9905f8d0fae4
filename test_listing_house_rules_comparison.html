<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>House Rules: Before vs After Enhancement</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .persian-green { color: #00a693; }
        .focus\:ring-persian-green:focus { --tw-ring-color: #00a693; }
        .focus\:border-persian-green:focus { --tw-border-opacity: 1; border-color: #00a693; }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">House Rules Enhancement Test</h1>
        <p class="text-center text-gray-600 mb-8">Airbnb Listing: <a href="https://www.airbnb.com/rooms/18839204" class="text-blue-600 hover:underline">Private Room near Harvard & Boston</a></p>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- BEFORE: Current System -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-red-600">
                    <i class="fas fa-times-circle mr-2"></i>BEFORE: Current System Issues
                </h2>
                
                <div class="space-y-3">
                    <div class="p-3 border border-red-200 bg-red-50 rounded">
                        <div class="flex items-center space-x-2 mb-1">
                            <input type="checkbox" checked class="h-4 w-4">
                            <span class="font-medium text-red-800">Duplicate Rules (6x)</span>
                        </div>
                        <div class="text-sm text-red-700 pl-6">
                            "5 guests maximum" appears 6 times!
                        </div>
                    </div>
                    
                    <div class="p-3 border border-red-200 bg-red-50 rounded">
                        <div class="flex items-center space-x-2 mb-1">
                            <input type="checkbox" checked class="h-4 w-4">
                            <span class="font-medium text-red-800">Conflicting Guest Limits</span>
                        </div>
                        <div class="text-sm text-red-700 pl-6">
                            "5 guests maximum" vs "2 guests maximum"
                        </div>
                    </div>
                    
                    <div class="p-3 border border-red-200 bg-red-50 rounded">
                        <div class="flex items-center space-x-2 mb-1">
                            <input type="checkbox" checked class="h-4 w-4">
                            <span class="font-medium text-red-800">Time Rules in House Rules</span>
                        </div>
                        <div class="text-sm text-red-700 pl-6">
                            "Check-in: 3:00 PM - 10:00 PM" (should be in Basic Info)
                        </div>
                    </div>
                    
                    <div class="p-3 border border-red-200 bg-red-50 rounded">
                        <div class="flex items-center space-x-2 mb-1">
                            <input type="checkbox" checked class="h-4 w-4">
                            <span class="font-medium text-red-800">Separate "Before you leave" Rules</span>
                        </div>
                        <div class="text-sm text-red-700 pl-6">
                            Multiple separate checkout instructions
                        </div>
                    </div>
                    
                    <div class="p-3 border border-red-200 bg-red-50 rounded">
                        <div class="flex items-center space-x-2 mb-1">
                            <input type="checkbox" checked class="h-4 w-4">
                            <span class="font-medium text-red-800">Non-Rule Content</span>
                        </div>
                        <div class="text-sm text-red-700 pl-6">
                            "Warm, private room near Harvard and Boston" (description, not rule)
                        </div>
                    </div>
                    
                    <div class="p-3 border border-red-200 bg-red-50 rounded">
                        <div class="flex items-center space-x-2 mb-1">
                            <input type="checkbox" checked class="h-4 w-4">
                            <span class="font-medium text-red-800">Conflicting Default Rules</span>
                        </div>
                        <div class="text-sm text-red-700 pl-6">
                            Default "No smoking" + Imported "No smoking" = duplicates
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 p-3 bg-gray-100 rounded">
                    <div class="text-sm font-medium text-gray-800 mb-2">Total Rules: 9 (with many duplicates)</div>
                    <div class="text-xs text-gray-600">Poor user experience, confusing AI responses</div>
                </div>
            </div>

            <!-- AFTER: Enhanced System -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4 text-green-600">
                    <i class="fas fa-check-circle mr-2"></i>AFTER: Enhanced System
                </h2>
                
                <div class="space-y-3">
                    <!-- Imported Rules (Enabled) -->
                    <div class="border-l-4 border-blue-500 pl-4 mb-4">
                        <h3 class="font-medium text-blue-800 mb-2">Imported Rules (Enabled)</h3>
                        
                        <div class="p-3 border border-blue-200 bg-blue-50 rounded mb-2">
                            <div class="flex items-center space-x-2 mb-1">
                                <input type="checkbox" checked class="h-4 w-4 text-persian-green">
                                <span class="font-medium">Property Capacity</span>
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">From Listing</span>
                            </div>
                            <div class="text-sm text-gray-600 pl-6">5 guests maximum</div>
                        </div>
                        
                        <div class="p-3 border border-blue-200 bg-blue-50 rounded mb-2">
                            <div class="flex items-center space-x-2 mb-1">
                                <input type="checkbox" checked class="h-4 w-4 text-persian-green">
                                <span class="font-medium">Quiet Hours</span>
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">From Listing</span>
                            </div>
                            <div class="text-sm text-gray-600 pl-6">Quiet hours from 11pm to 8am</div>
                        </div>
                        
                        <div class="p-3 border border-blue-200 bg-blue-50 rounded mb-2">
                            <div class="flex items-center space-x-2 mb-1">
                                <input type="checkbox" checked class="h-4 w-4 text-persian-green">
                                <span class="font-medium">Parties and Events</span>
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">From Listing</span>
                            </div>
                            <div class="text-sm text-gray-600 pl-6">No parties or events</div>
                        </div>
                        
                        <div class="p-3 border border-blue-200 bg-blue-50 rounded mb-2">
                            <div class="flex items-center space-x-2 mb-1">
                                <input type="checkbox" checked class="h-4 w-4 text-persian-green">
                                <span class="font-medium">Smoking</span>
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">From Listing</span>
                            </div>
                            <div class="text-sm text-gray-600 pl-6">No smoking</div>
                        </div>
                        
                        <div class="p-3 border border-blue-200 bg-blue-50 rounded mb-2">
                            <div class="flex items-center space-x-2 mb-1">
                                <input type="checkbox" checked class="h-4 w-4 text-persian-green">
                                <span class="font-medium">Before you leave</span>
                                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">From Listing</span>
                            </div>
                            <div class="text-sm text-gray-600 pl-6">Before you leave, clean up and lock up</div>
                        </div>
                    </div>
                    
                    <!-- Default Rules (Disabled) -->
                    <div class="border-l-4 border-gray-400 pl-4">
                        <h3 class="font-medium text-gray-700 mb-2">Available Default Rules (Disabled)</h3>
                        
                        <div class="p-3 border border-gray-200 rounded mb-2">
                            <div class="flex items-center space-x-2 mb-1">
                                <input type="checkbox" class="h-4 w-4">
                                <span class="font-medium text-gray-700">Pets</span>
                                <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">Common Rule</span>
                            </div>
                            <div class="text-sm text-gray-500 pl-6">No pets allowed unless specifically approved</div>
                        </div>
                        
                        <div class="p-3 border border-gray-200 rounded">
                            <div class="flex items-center space-x-2 mb-1">
                                <input type="checkbox" class="h-4 w-4">
                                <span class="font-medium text-gray-700">Shoes in Property</span>
                                <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">Common Rule</span>
                            </div>
                            <div class="text-sm text-gray-500 pl-6">Remove shoes when entering the property</div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 p-3 bg-green-100 rounded">
                    <div class="text-sm font-medium text-green-800 mb-2">Total Rules: 7 (5 imported + 2 default)</div>
                    <div class="text-xs text-green-600">Clean, organized, no duplicates or conflicts</div>
                </div>
            </div>
        </div>
        
        <!-- Enhancement Summary -->
        <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4 text-center">
                <i class="fas fa-magic mr-2 text-purple-600"></i>Enhancement Summary
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="text-3xl font-bold text-red-600 mb-2">18 → 7</div>
                    <div class="text-sm text-gray-600">Rules Reduced</div>
                    <div class="text-xs text-gray-500 mt-1">Eliminated duplicates</div>
                </div>
                
                <div class="text-center">
                    <div class="text-3xl font-bold text-green-600 mb-2">100%</div>
                    <div class="text-sm text-gray-600">Conflicts Resolved</div>
                    <div class="text-xs text-gray-500 mt-1">No duplicate topics</div>
                </div>
                
                <div class="text-center">
                    <div class="text-3xl font-bold text-blue-600 mb-2">1</div>
                    <div class="text-sm text-gray-600">Checkout Rule</div>
                    <div class="text-xs text-gray-500 mt-1">Concatenated instructions</div>
                </div>
            </div>
            
            <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                <h3 class="font-semibold text-blue-800 mb-2">Key Improvements:</h3>
                <ul class="text-sm text-blue-700 space-y-1">
                    <li>✅ <strong>Deduplication:</strong> Removed 11 duplicate rules</li>
                    <li>✅ <strong>Conflict Resolution:</strong> Filtered conflicting default rules</li>
                    <li>✅ <strong>Rule Concatenation:</strong> Combined "Before you leave" instructions</li>
                    <li>✅ <strong>Content Filtering:</strong> Removed non-rule content and time rules</li>
                    <li>✅ <strong>Simplified Structure:</strong> Content field instead of title + description</li>
                    <li>✅ <strong>Dynamic Saving:</strong> Auto-save on all changes</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
