<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced House Rules Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .persian-green { color: #00a693; }
        .focus\:ring-persian-green:focus { --tw-ring-color: #00a693; }
        .focus\:border-persian-green:focus { --tw-border-opacity: 1; border-color: #00a693; }
    </style>
</head>
<body class="bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-2xl font-bold text-center mb-8">Enhanced House Rules System Test</h1>
        
        <!-- Test Data Input -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">Test Data</h2>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Simulated Imported Rules (JSON)</label>
                    <textarea id="imported-rules-input" rows="8" class="w-full p-3 border border-gray-300 rounded-lg text-sm font-mono">
[
  {"title": "No smoking", "description": "No smoking anywhere on the property"},
  {"title": "5 guests maximum", "description": "Maximum 5 guests allowed"},
  {"title": "Quiet hours", "description": "Quiet hours: 9:00 PM - 7:00 AM"},
  {"title": "No parties", "description": "No parties or events"},
  {"title": "Before you leave", "description": "Before you leave, gather used towels"},
  {"title": "Checkout cleaning", "description": "Before you leave, throw trash away"},
  {"title": "Lock up", "description": "Before you leave, turn things off and lock up"},
  {"title": "2 guests maximum", "description": "2 guests maximum"},
  {"title": "Check-in time", "description": "Check-in: 3:00 PM - 10:00 PM"}
]</textarea>
                </div>
                <button onclick="processTestRules()" 
                        class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium">
                    Process Test Rules
                </button>
            </div>
        </div>

        <!-- Results Display -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Processed Rules</h2>
            <div id="rules-display" class="space-y-4">
                <!-- Rules will be rendered here -->
            </div>
        </div>

        <!-- Test Results -->
        <div class="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 class="font-semibold text-blue-800 mb-2">Expected Results:</h3>
            <ul class="text-sm text-blue-700 list-disc list-inside space-y-1">
                <li>✅ "Before you leave" rules should be concatenated into single rule</li>
                <li>✅ Duplicate guest maximum rules should be deduplicated</li>
                <li>✅ Check-in/out time rules should be filtered out</li>
                <li>✅ Rules should use simplified content structure</li>
                <li>✅ Default rules should not conflict with imported ones</li>
                <li>✅ Custom rules should have single content field</li>
            </ul>
            <div id="test-results" class="mt-4 p-3 bg-white rounded border">
                Test results will appear here...
            </div>
        </div>
    </div>

    <script>
        // Simulate the enhanced house rules processing
        class HouseRulesProcessor {
            constructor() {
                this.defaultRules = [
                    {
                        id: 'smoking',
                        title: 'Smoking',
                        content: 'No smoking anywhere on the property',
                        enabled: false,
                        type: 'default'
                    },
                    {
                        id: 'parties',
                        title: 'Parties and Events',
                        content: 'No parties or events permitted',
                        enabled: false,
                        type: 'default'
                    },
                    {
                        id: 'quiet_hours',
                        title: 'Quiet Hours',
                        content: 'Keep noise to a minimum between 10 PM and 8 AM',
                        enabled: false,
                        type: 'default'
                    },
                    {
                        id: 'pets',
                        title: 'Pets',
                        content: 'No pets allowed unless specifically approved',
                        enabled: false,
                        type: 'default'
                    },
                    {
                        id: 'occupancy',
                        title: 'Property Capacity',
                        content: 'Respect the maximum occupancy limit',
                        enabled: false,
                        type: 'default'
                    },
                    {
                        id: 'shoes',
                        title: 'Shoes in Property',
                        content: 'Remove shoes when entering the property',
                        enabled: false,
                        type: 'default'
                    }
                ];
            }

            processBeforeYouLeaveRules(rules) {
                const beforeYouLeaveRules = [];
                const otherRules = [];

                rules.forEach(rule => {
                    if (!rule || !rule.description) {
                        otherRules.push(rule);
                        return;
                    }

                    const description = rule.description.toLowerCase();
                    const title = (rule.title || '').toLowerCase();

                    if (description.includes('before you leave') || 
                        description.includes('before leaving') ||
                        description.includes('when you leave') ||
                        title.includes('before you leave') ||
                        title.includes('before leaving')) {
                        beforeYouLeaveRules.push(rule);
                    } else {
                        otherRules.push(rule);
                    }
                });

                if (beforeYouLeaveRules.length > 0) {
                    console.log(`🔗 Found ${beforeYouLeaveRules.length} "Before you leave" rules, concatenating...`);
                    
                    const instructions = beforeYouLeaveRules.map(rule => {
                        let instruction = rule.description;
                        instruction = instruction.replace(/^before you leave[,:]\s*/i, '');
                        instruction = instruction.replace(/^before leaving[,:]\s*/i, '');
                        instruction = instruction.replace(/^when you leave[,:]\s*/i, '');
                        instruction = instruction.trim();
                        if (instruction && !instruction.endsWith('.') && !instruction.endsWith(',')) {
                            instruction += '.';
                        }
                        return instruction;
                    }).filter(instruction => instruction.length > 0);

                    if (instructions.length > 0) {
                        const concatenatedRule = {
                            id: 'before_you_leave_combined',
                            title: 'Before you leave',
                            description: `Before you leave, ${instructions.join(' ').replace(/\.\s+/g, ', ').replace(/,$/, '.')}`,
                            content: `Before you leave, ${instructions.join(' ').replace(/\.\s+/g, ', ').replace(/,$/, '.')}`,
                            type: 'imported'
                        };

                        console.log('✅ Created concatenated "Before you leave" rule:', concatenatedRule.description);
                        otherRules.push(concatenatedRule);
                    }
                }

                return otherRules;
            }

            deduplicateRules(rules) {
                const seen = new Set();
                const deduplicated = [];

                rules.forEach(rule => {
                    if (!rule || !rule.description) return;
                    const normalizedDescription = rule.description.toLowerCase().trim();
                    if (!seen.has(normalizedDescription)) {
                        seen.add(normalizedDescription);
                        deduplicated.push(rule);
                    } else {
                        console.log(`🚫 Filtered duplicate rule: "${rule.description}"`);
                    }
                });

                console.log(`📋 Deduplicated rules: ${rules.length} → ${deduplicated.length}`);
                return deduplicated;
            }

            filterValidRules(rules) {
                return rules.filter(rule => {
                    const description = rule.description?.toLowerCase() || '';
                    
                    // Filter out check-in/check-out time rules
                    const timeRulePatterns = [
                        'check-in after', 'check-in before', 'check-in time', 'check-in is',
                        'check-out before', 'check-out by', 'check-out time', 'check-out is',
                        'check-in:', 'check-out:'
                    ];

                    const isTimeRule = timeRulePatterns.some(pattern => description.includes(pattern));
                    if (isTimeRule) {
                        console.log(`🚫 Filtered time rule: "${rule.description}"`);
                        return false;
                    }

                    return true;
                });
            }

            rulesConflict(defaultRule, importedRule) {
                const importedContent = (importedRule.content || importedRule.description || '').toLowerCase();

                const conflictMappings = [
                    { defaultIds: ['pets'], patterns: ['pet', 'animal', 'dog', 'cat', 'no pets'] },
                    { defaultIds: ['smoking'], patterns: ['smok', 'no smoking', 'cigarette'] },
                    { defaultIds: ['parties'], patterns: ['part', 'event', 'gathering', 'no parties'] },
                    { defaultIds: ['quiet_hours'], patterns: ['quiet', 'noise', 'silent'] },
                    { defaultIds: ['occupancy'], patterns: ['guest', 'occupancy', 'maximum', 'capacity'] }
                ];

                for (const mapping of conflictMappings) {
                    const defaultMatches = mapping.defaultIds.includes(defaultRule.id);
                    const importedMatches = mapping.patterns.some(pattern => importedContent.includes(pattern));

                    if (defaultMatches && importedMatches) {
                        console.log(`🚫 Conflict detected: Default rule "${defaultRule.title}" conflicts with imported rule "${importedRule.title || importedRule.description}"`);
                        return true;
                    }
                }

                return false;
            }

            filterDefaultRulesForConflicts(defaultRules, importedRules) {
                return defaultRules.filter(defaultRule => {
                    const hasConflict = importedRules.some(importedRule => {
                        return this.rulesConflict(defaultRule, importedRule);
                    });
                    return !hasConflict;
                });
            }

            processRules(importedRules) {
                console.log('🔧 Processing house rules...');
                
                // Step 1: Process "Before you leave" rules
                const processedRules = this.processBeforeYouLeaveRules(importedRules);
                
                // Step 2: Deduplicate rules
                const deduplicatedRules = this.deduplicateRules(processedRules);
                
                // Step 3: Filter out invalid rules
                const validImportedRules = this.filterValidRules(deduplicatedRules);
                
                // Step 4: Filter default rules for conflicts
                const filteredDefaultRules = this.filterDefaultRulesForConflicts(this.defaultRules, validImportedRules);
                
                // Step 5: Combine rules
                const combinedRules = [
                    ...validImportedRules.map(rule => ({ ...rule, enabled: true, source: 'imported' })),
                    ...filteredDefaultRules.map(rule => ({ ...rule, enabled: false, source: 'default' }))
                ];

                console.log(`✅ Final rules: ${combinedRules.length} total (${validImportedRules.length} imported, ${filteredDefaultRules.length} default)`);
                return combinedRules;
            }
        }

        const processor = new HouseRulesProcessor();

        function processTestRules() {
            try {
                const input = document.getElementById('imported-rules-input').value;
                const importedRules = JSON.parse(input);
                
                console.clear();
                console.log('🧪 Testing enhanced house rules processing...');
                
                const processedRules = processor.processRules(importedRules);
                
                renderRules(processedRules);
                showTestResults(processedRules);
                
            } catch (error) {
                console.error('Error processing rules:', error);
                document.getElementById('test-results').innerHTML = `❌ Error: ${error.message}`;
            }
        }

        function renderRules(rules) {
            const container = document.getElementById('rules-display');
            
            let html = '';
            rules.forEach((rule, index) => {
                const isImported = rule.source === 'imported';
                const sourceLabel = isImported ? 'From Listing' : 'Common Rule';
                const sourceClass = isImported ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-600';
                const ruleContent = rule.content || rule.description || '';

                html += `
                    <div class="border rounded-lg p-4 ${isImported ? 'border-blue-200 bg-blue-50' : 'border-gray-200'}">
                        <div class="flex items-start space-x-3">
                            <input type="checkbox" ${rule.enabled ? 'checked' : ''} 
                                   class="mt-1 h-4 w-4 text-persian-green border-gray-300 rounded">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-2">
                                    <label class="font-medium text-gray-900">${rule.title || 'House Rule'}</label>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${sourceClass}">
                                        ${sourceLabel}
                                    </span>
                                </div>
                                <div class="text-sm text-gray-600 p-2 bg-gray-50 rounded border">
                                    ${ruleContent}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function showTestResults(rules) {
            const importedRules = rules.filter(r => r.source === 'imported');
            const defaultRules = rules.filter(r => r.source === 'default');
            
            const beforeYouLeaveRule = importedRules.find(r => r.title === 'Before you leave');
            const hasBeforeYouLeave = beforeYouLeaveRule && beforeYouLeaveRule.content.includes('gather used towels') && 
                                     beforeYouLeaveRule.content.includes('throw trash away') && 
                                     beforeYouLeaveRule.content.includes('lock up');
            
            const hasNoDuplicateGuests = importedRules.filter(r => r.description.includes('guests maximum')).length <= 1;
            const hasNoTimeRules = !importedRules.some(r => r.description.includes('Check-in:'));
            
            let results = `
                <div class="space-y-2">
                    <div class="${hasBeforeYouLeave ? 'text-green-600' : 'text-red-600'}">
                        ${hasBeforeYouLeave ? '✅' : '❌'} Before you leave concatenation: ${hasBeforeYouLeave ? 'PASS' : 'FAIL'}
                    </div>
                    <div class="${hasNoDuplicateGuests ? 'text-green-600' : 'text-red-600'}">
                        ${hasNoDuplicateGuests ? '✅' : '❌'} Duplicate guest rules removed: ${hasNoDuplicateGuests ? 'PASS' : 'FAIL'}
                    </div>
                    <div class="${hasNoTimeRules ? 'text-green-600' : 'text-red-600'}">
                        ${hasNoTimeRules ? '✅' : '❌'} Time rules filtered out: ${hasNoTimeRules ? 'PASS' : 'FAIL'}
                    </div>
                    <div class="text-blue-600">
                        📊 Total rules: ${rules.length} (${importedRules.length} imported, ${defaultRules.length} default)
                    </div>
                </div>
            `;
            
            document.getElementById('test-results').innerHTML = results;
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(processTestRules, 500);
        });
    </script>
</body>
</html>
