# Host Dashboard Rebuild Plan

## Current State Analysis

### Existing Templates & Functionality
1. **host_dashboard.html** - Basic dashboard with profile info and properties overview
2. **profile.html** - User profile editing (name, email, phone, role)
3. **properties_list.html** - Property listing with CRUD operations
4. **property_edit.html** - Property editing form (basic details, check-in/out times, WiFi)
5. **property_reservations.html** - Reservation management (iCal sync, guest contacts, magic links)
6. **knowledge_unified.html** - Knowledge base management (file upload, text input, approval workflow)
7. **property_conversations.html** - Conversation viewing and management

### Current Functionality That Works
- ✅ Property creation/editing (basic details)
- ✅ Property listing with management options
- ✅ iCal integration for reservation sync
- ✅ Guest contact info management
- ✅ Magic link generation for guest access
- ✅ Knowledge base file upload and text input
- ✅ Knowledge item approval workflow
- ✅ Conversation viewing with message history
- ✅ User profile management

### Current Styling
- Bootstrap 5 with custom CSS variables
- Guest dashboard uses Tailwind + custom color palette
- Need to align host dashboard with Guest dashboard styling

## New Structure Overview

### 1. Properties Screen
- Property list with Active/Inactive toggles
- Add/Edit properties (basic details + knowledge combined)
- Knowledge base setup & management per property
- Property settings (email templates, maintenance reminders)

### 2. Calendar Screen  
- Reservation timeline view (Gantt-like) or list view
- Guest contact management
- Magic link generation
- Maintenance/cleaning scheduling
- Task management (scheduled, in progress, done)

### 3. Support Center Screen
- Task board with filterable items
- Conversations feed with search/filter
- Alerts zone for real-time concerns
- AI-flagged issues and insights

### 4. Settings Modal
- Host profile & preferences
- Notification settings
- Team management

## Step-by-Step Implementation Plan

### Phase 1: Foundation & Styling Alignment (Priority 1)
1. **Create new main host dashboard template**
   - ✅ Implement three-screen navigation structure
   - ✅ Adopt Guest dashboard color palette and fonts
   - ✅ Make responsive layout that maximizes screen space
   - ✅ Add Tailwind CSS integration

2. **Migrate existing functionality to Properties Screen**
   - ✅ Move properties list from host_dashboard.html
   - ✅ Create comprehensive property management modal with tabbed interface
   - ✅ Implement inline property editing with click-to-edit fields
   - ✅ Integrate advanced knowledge base management with filtering
   - ✅ Add knowledge item CRUD operations (approve, reject, delete, expand)
   - ✅ Implement real-time statistics and visual status indicators
   - 🔧 FIX NEEDED: Property Active/Inactive toggle functionality
   - 🔧 FIX NEEDED: WiFi details display and API endpoint creation

3. **Create Calendar Screen foundation**
   - ✅ Move reservation management from property_reservations.html
   - ✅ Implement timeline/list view toggle with proper reservation spanning
   - ✅ Create comprehensive reservation details modal
   - ✅ Implement guest contact management with editing capabilities
   - ✅ Add magic link generation and tracking
   - ✅ Integrate email communications (welcome, reminder, review)
   - ✅ Add reservation status management and filtering

4. **Create Support Center Screen foundation**
   - ✅ Move conversations from property_conversations.html
   - ✅ Create basic task board layout
   - ✅ Implement conversation feed with search/filter

5. **Create Settings Modal**
   - ✅ Move profile editing from profile.html with comprehensive form
   - ✅ Add PIN change functionality with secure validation
   - ✅ Implement account statistics display
   - ✅ Create tabbed settings interface (Profile, Notifications, Team)
   - ✅ Add real-time form validation and auto-save

## Current Issues to Fix (Immediate Priority)

### Properties Screen Fixes
- 🔧 **Property Status Toggle**: Fix Active/Inactive toggle functionality with Firestore integration
- 🔧 **WiFi Details Display**: Fix showing "Not configured" for existing WiFi data
- 🔧 **API Endpoint**: Create missing PUT /api/properties/{id} endpoint
- 🔧 **Knowledge Type Filtering**: Fix type-based filtering not working properly
- 🔧 **UI Button Updates**: Remove Edit button, rename Knowledge to "Manage", add Conversations button
- 🔧 **Property Configuration**: Move basic info to Configuration tab, add Delete Property functionality

### Phase 2: Enhanced Functionality (Priority 2)
6. **Properties Screen enhancements**
   - 🔄 TODO: Add email template management per property
   - 🔄 TODO: Add maintenance reminder settings
   - 🔄 TODO: Enhance knowledge base categorization

7. **Calendar Screen enhancements**
   - 🔄 TODO: Implement Gantt-like calendar view
   - 🔄 TODO: Add maintenance/cleaning task scheduling
   - 🔄 TODO: Add task status management (scheduled, in progress, done)
   - 🔄 TODO: Add cleaner assignment notifications

8. **Support Center Screen enhancements**
   - 🔄 TODO: Add AI-powered conversation insights
   - 🔄 TODO: Add sentiment analysis indicators
   - 🔄 TODO: Add real-time alerts zone
   - 🔄 TODO: Add AI-flagged issue detection

### Phase 3: Advanced Features (Priority 3)
9. **Cross-property functionality**
   - 🔄 TODO: Add multi-property view toggle
   - 🔄 TODO: Add property filtering across all screens
   - 🔄 TODO: Add consolidated reporting

10. **Team management**
    - 🔄 TODO: Add cleaner/maintenance crew role management
    - 🔄 TODO: Add task assignment system
    - 🔄 TODO: Add notification preferences per team member

11. **AI enhancements**
    - 🔄 TODO: Add trending issues detection
    - 🔄 TODO: Add missing knowledge base item suggestions
    - 🔄 TODO: Add automated task creation from guest conversations

## File Structure Changes

### New Files to Create
- `templates/host_dashboard_new.html` - Main three-screen dashboard
- `static/js/host_dashboard_new.js` - Enhanced JavaScript functionality
- `static/css/host_dashboard.css` - Host-specific styling with Guest dashboard alignment

### Files to Modify
- `templates/host_dashboard.html` - Replace with new structure
- `static/js/host_dashboard.js` - Enhance with new functionality
- `static/css/style.css` - Add host dashboard color palette

### Files to Deprecate (keep for reference)
- `templates/profile.html` - Functionality moved to Settings modal
- `templates/properties_list.html` - Functionality moved to Properties screen
- `templates/property_edit.html` - Functionality moved to Properties screen
- `templates/property_reservations.html` - Functionality moved to Calendar screen
- `templates/knowledge_unified.html` - Functionality moved to Properties screen
- `templates/property_conversations.html` - Functionality moved to Support Center screen

## Color Palette & Styling Guidelines

### Color Palette (from Guest Dashboard)
- **Primary**: Persian Green (#2a9d8f)
- **Secondary**: Saffron (#e9c46a)
- **Dark**: Dark Purple (#161032)
- **Light**: Light Cyan (#e0fbfc)
- **Accent**: Bittersweet (#ee6055)

### Typography
- **Primary Font**: Plus Jakarta Sans
- **Secondary Font**: Noto Sans
- **Font Weights**: 400 (regular), 500 (medium), 700 (bold), 800/900 (extra bold)

### Layout Principles
- Use Tailwind CSS for consistency
- Maximize screen space usage
- Responsive design for all screen sizes
- Modern card-based layouts
- Consistent spacing and typography

## Implementation Notes

### Existing Functionality to Preserve
1. **Property Management**: All CRUD operations must continue working
2. **iCal Integration**: Reservation sync must remain functional
3. **Magic Links**: Guest access system must be preserved
4. **Knowledge Base**: File upload and approval workflow must work
5. **Conversation History**: All existing conversation data must be accessible

### New Functionality to Add
1. **Property Status Toggle**: Active/Inactive with visual indicators
2. **Calendar Views**: Timeline (Gantt-like) and list view options
3. **Task Management**: Cleaning/maintenance scheduling system
4. **AI Insights**: Conversation analysis and trending issues
5. **Team Management**: Role-based access for cleaning crews

### Technical Considerations
1. **Responsive Design**: Must work on mobile, tablet, and desktop
2. **Performance**: Lazy loading for large datasets
3. **Real-time Updates**: WebSocket integration for live updates
4. **Accessibility**: ARIA labels and keyboard navigation
5. **SEO**: Proper meta tags and structured data

This plan ensures that existing functionality is preserved while introducing the new three-screen structure with modern styling and enhanced features. 