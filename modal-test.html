<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Property Setup Modal Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Modal Height Adjustments for Short Screens */
        @media (max-height: 930px) {
            /* Ensure modal fits on shorter screens - 95vh makes better use of space */
            #property-setup-modal .bg-white.rounded-lg.shadow-xl {
                max-height: 95vh !important;
            }

            #setup-step-content {
                max-height: calc(95vh - 220px) !important;
            }
        }

        @media (max-height: 700px) {
            /* For very short screens */
            #property-setup-modal .bg-white.rounded-lg.shadow-xl {
                max-height: 95vh !important;
            }

            #setup-step-content {
                max-height: calc(95vh - 200px) !important;
            }

            /* Reduce footer padding further on very short screens */
            #property-setup-modal .modal-footer {
                padding: 0.5rem 1.5rem !important;
            }
        }

        /* Step Names Responsive Behavior - Ultra-specific CSS to override everything */
        @media screen and (max-width: 500px) {
            /* Hide step names and show icons on narrow screens - iPhone 14 Pro Max is 430px */
            div#property-setup-modal .step-progress-text {
                display: none !important;
                visibility: hidden !important;
            }

            div#property-setup-modal .step-progress-icon {
                display: inline-block !important;
                visibility: visible !important;
            }

            /* Also hide button text and reduce padding */
            div#property-setup-modal .btn-text {
                display: none !important;
                visibility: hidden !important;
            }

            /* Hide step indicator completely on narrow screens */
            div#property-setup-modal #step-indicator {
                display: none !important;
                visibility: hidden !important;
            }

            /* Reduce padding across the whole modal for better space use */
            div#property-setup-modal .modal-footer {
                padding: 1rem !important;
            }

            div#property-setup-modal .p-6 {
                padding: 1rem !important;
            }
        }

        @media screen and (min-width: 501px) {
            /* Show step names and hide icons on wider screens */
            div#property-setup-modal .step-progress-text {
                display: inline !important;
                visibility: visible !important;
            }

            div#property-setup-modal .step-progress-icon {
                display: none !important;
                visibility: hidden !important;
            }

            /* Show button text */
            div#property-setup-modal .btn-text {
                display: inline !important;
                visibility: visible !important;
            }
        }


        
        .persian-green {
            background-color: #2a9d8f;
        }
        
        .persian-green:hover {
            background-color: #238a7a;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto p-8">
        <h1 class="text-2xl font-bold mb-4">Property Setup Modal Test</h1>
        <p class="mb-4">Test the modal on different screen sizes:</p>
        <ul class="mb-6 space-y-2">
            <li><strong>Height &lt; 930px:</strong> Modal should fit properly</li>
            <li><strong>Width &lt; 480px:</strong> Step names should become icons</li>
        </ul>
        
        <button onclick="openTestModal()" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
            Open Property Setup Modal
        </button>
        
        <div class="mt-4 text-sm text-gray-600">
            <p>Current screen: <span id="screen-size"></span></p>
        </div>
    </div>

    <script>
        // Update screen size display
        function updateScreenSize() {
            document.getElementById('screen-size').textContent = 
                `${window.innerWidth}px × ${window.innerHeight}px`;
        }
        updateScreenSize();
        window.addEventListener('resize', updateScreenSize);

        function openTestModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-75 z-50 flex items-center justify-center p-4';
            modal.id = 'property-setup-modal';

            modal.innerHTML = `
                <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                    <!-- Header -->
                    <div class="p-6 text-white" style="background: linear-gradient(to right, #2a9d8f, #e9c46a);">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-xl font-semibold text-white">
                                    <i class="fas fa-cog mr-2"></i>Property Setup
                                </h3>
                                <p class="text-white opacity-90 text-sm mt-1">Test Property</p>
                            </div>
                            <button onclick="closeTestModal()" class="text-white hover:text-gray-200">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                        
                        <!-- Progress Bar -->
                        <div class="mt-4">
                            <div class="flex items-center justify-between text-sm mb-2 text-white">
                                <span>Step 2 of 5</span>
                                <span>40% Complete</span>
                            </div>
                            <div class="w-full bg-white/20 rounded-full h-2">
                                <div class="bg-white rounded-full h-2 transition-all duration-300" style="width: 40%"></div>
                            </div>
                            <div class="flex justify-between text-xs mt-2 text-white">
                                <span class="font-medium opacity-100">
                                    <span class="step-progress-text" style="display: inline;">Basic Information</span>
                                    <i class="fas fa-info-circle step-progress-icon" style="display: none;" title="Basic Information"></i>
                                </span>
                                <span class="font-medium opacity-100">
                                    <span class="step-progress-text" style="display: inline;">House Rules</span>
                                    <i class="fas fa-gavel step-progress-icon" style="display: none;" title="House Rules"></i>
                                </span>
                                <span class="opacity-80">
                                    <span class="step-progress-text" style="display: inline;">Emergency Information</span>
                                    <i class="fas fa-exclamation-triangle step-progress-icon" style="display: none;" title="Emergency Information"></i>
                                </span>
                                <span class="opacity-80">
                                    <span class="step-progress-text" style="display: inline;">Other Information</span>
                                    <i class="fas fa-clipboard-list step-progress-icon" style="display: none;" title="Other Information"></i>
                                </span>
                                <span class="opacity-80">
                                    <span class="step-progress-text" style="display: inline;">Review and Approve</span>
                                    <i class="fas fa-check-circle step-progress-icon" style="display: none;" title="Review and Approve"></i>
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Content -->
                    <div class="p-6 overflow-y-auto" id="setup-step-content" style="max-height: calc(90vh - 220px);">
                        <h4 class="text-lg font-semibold mb-4">Test Content</h4>
                        <div class="space-y-4">
                            ${Array.from({length: 20}, (_, i) => 
                                `<div class="p-4 bg-gray-50 rounded-lg">
                                    <h5 class="font-medium">Test Item ${i + 1}</h5>
                                    <p class="text-gray-600">This is test content to check scrolling behavior. The modal should always keep the navigation buttons visible at the bottom.</p>
                                </div>`
                            ).join('')}
                        </div>
                    </div>

                    <!-- Footer -->
                    <div class="modal-footer bg-gray-50 px-6 py-4 flex justify-between items-center border-t">
                        <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                            <i class="fas fa-arrow-left mr-2"></i><span class="btn-text">Previous</span>
                        </button>

                        <div id="step-indicator" class="text-sm text-gray-600">
                            Step 2 of 5: House Rules
                        </div>

                        <button class="px-4 py-2 persian-green text-white rounded-lg hover:persian-green/90 transition-colors">
                            <span class="btn-text">Next</span> <i class="fas fa-arrow-right ml-2"></i>
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            document.body.style.overflow = 'hidden';

            // Apply responsive behavior immediately
            applyResponsiveStyles();

            // Add resize listener for orientation changes
            window.addEventListener('resize', applyResponsiveStyles);

            // Close on outside click
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeTestModal();
                }
            });
        }

        function applyResponsiveStyles() {
            // Force responsive behavior with JavaScript
            const isNarrowScreen = window.innerWidth <= 500;
            console.log('Screen width:', window.innerWidth, 'Is narrow:', isNarrowScreen);

            const stepTexts = document.querySelectorAll('#property-setup-modal .step-progress-text');
            const stepIcons = document.querySelectorAll('#property-setup-modal .step-progress-icon');
            const btnTexts = document.querySelectorAll('#property-setup-modal .btn-text');
            const stepIndicator = document.getElementById('step-indicator');

            if (isNarrowScreen) {
                // Hide text, show icons
                stepTexts.forEach(el => {
                    el.style.display = 'none';
                    el.style.visibility = 'hidden';
                });
                stepIcons.forEach(el => {
                    el.style.display = 'inline-block';
                    el.style.visibility = 'visible';
                });
                btnTexts.forEach(el => {
                    el.style.display = 'none';
                    el.style.visibility = 'hidden';
                });
                // Hide step indicator completely on narrow screens
                if (stepIndicator) {
                    stepIndicator.style.display = 'none';
                    stepIndicator.style.visibility = 'hidden';
                }
            } else {
                // Show text, hide icons
                stepTexts.forEach(el => {
                    el.style.display = 'inline';
                    el.style.visibility = 'visible';
                });
                stepIcons.forEach(el => {
                    el.style.display = 'none';
                    el.style.visibility = 'hidden';
                });
                btnTexts.forEach(el => {
                    el.style.display = 'inline';
                    el.style.visibility = 'visible';
                });
                // Show step indicator on wider screens
                if (stepIndicator) {
                    stepIndicator.style.display = 'block';
                    stepIndicator.style.visibility = 'visible';
                }
            }
        }

        function closeTestModal() {
            const modal = document.getElementById('property-setup-modal');
            if (modal) {
                modal.remove();
                document.body.style.overflow = 'auto';
                window.removeEventListener('resize', applyResponsiveStyles);
            }
        }
    </script>
</body>
</html>
